# Email System Backend Service

基于 Clean Architecture 和 DDD 设计的邮件系统后端服务。

## 项目结构

```
email-system/
├── cmd/                        # 入口(main)，生命周期、DI、服务注册
├── api/                        # proto 或 HTTP 文档
├── configs/                    # 配置模板
├── internal/
│   ├── interfaces/             # HTTP/gRPC/web 适配层
│   │   ├── http/               # HTTP 接口
│   │   └── grpc/              # gRPC 接口
│   ├── application/            # 应用服务与用例
│   ├── domain/                 # 领域模型、仓储接口
│   └── infrastructure/         # 持久化/外部依赖实现
│       ├── container/          # 依赖注入容器
│       ├── persistence/        # 持久化实现
│       ├── adapter/            # 适配器
│       └── external/           # 外部服务客户端
├── pkg/                        # 本服务内共享包
└── scripts/                    # 脚本
```

## 核心功能

- 邮件账户管理：支持多种邮件服务提供商（SMTP、API等）
- 邮件模板管理：创建、编辑和管理邮件模板
- 邮件发送：发送单封或批量邮件，支持模板渲染
- 发送队列：异步发送队列，支持重试机制
- 多租户支持：租户隔离，每个租户有独立的配置和限额
- 统计与监控：发送状态跟踪、打开率、点击率等统计

## 技术栈

- Go 1.20+
- Gin Web 框架
- GORM 数据库 ORM
- Clean Architecture + DDD 架构
- OpenTelemetry 可观测性
- Nacos 配置中心

## 开发规范

- 遵循 Clean Architecture 分层：接口层、应用层、领域层、基础设施层
- 仅允许 GET/POST 方法；路由不使用 path 参数
- 统一响应结构，严禁返回内部错误细节
- internalAppId 做隔离、tenantId 做归属
- 构造函数注入，禁止运行时 nil 检查
- 所有 DB 调用使用 WithContext(ctx) 且无自动预加载
- 关联查询批量加载，避免 N+1
- DTO 校验 + 统一错误翻译
- 外部调用具备超时/重试/熔断/限流
- 日志结构化 + trace 贯穿 + 敏感字段脱敏

## 启动服务

```bash
# 编译
go build -o email-system ./cmd

# 运行
./email-system
```

## 配置

配置文件位于 `configs/` 目录下，支持从 Nacos 配置中心加载配置。

## API 文档

TODO: 添加 API 文档链接

## 错误码

邮件系统错误码区间：
- 邮件账户/发件配置：120000-120099
- 邮件模板：120100-120199
- 邮件发送/任务：120200-120299
- 附件与存储：120300-120399
- 第三方服务：120800-120899
- 系统错误：120900-120999
