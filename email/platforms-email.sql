/*
 Navicat Premium Data Transfer

 Source Server         : nas-mysql-remote
 Source Server Type    : MySQL
 Source Server Version : 90100 (9.1.0)
 Source Host           : **************:3308
 Source Schema         : platforms-email

 Target Server Type    : MySQL
 Target Server Version : 90100 (9.1.0)
 File Encoding         : 65001

 Date: 07/08/2025 21:12:56
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for email_accounts
-- ----------------------------
DROP TABLE IF EXISTS `email_accounts`;
CREATE TABLE `email_accounts` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint NOT NULL,
  `tenant_id` bigint NOT NULL,
  `internal_app_id` bigint NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` bigint NOT NULL,
  `provider` varchar(100) NOT NULL,
  `host` varchar(255) DEFAULT NULL,
  `port` bigint DEFAULT '0',
  `username` varchar(255) DEFAULT NULL,
  `password` varchar(500) DEFAULT NULL,
  `from_address` varchar(255) NOT NULL,
  `from_name` varchar(255) DEFAULT NULL,
  `reply_to_address` varchar(255) DEFAULT NULL,
  `is_ssl` tinyint(1) DEFAULT '1',
  `is_active` tinyint(1) DEFAULT '1',
  `daily_limit` bigint DEFAULT '0',
  `monthly_limit` bigint DEFAULT '0',
  `sent_today` bigint DEFAULT '0',
  `sent_this_month` bigint DEFAULT '0',
  `last_sent_at` datetime(3) DEFAULT NULL,
  `test_status` bigint DEFAULT '0',
  `test_message` text,
  `config` json DEFAULT NULL,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `created_by` varchar(50) DEFAULT NULL,
  `updated_by` varchar(50) DEFAULT NULL,
  `version` bigint DEFAULT '1',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否为系统内置账号，TRUE表示系统内置，需要超级管理员权限',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_email_accounts_account_id` (`account_id`),
  KEY `idx_email_accounts_tenant_id` (`tenant_id`),
  KEY `idx_email_accounts_deleted_at` (`deleted_at`),
  KEY `idx_email_accounts_is_system` (`tenant_id`,`is_system`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of email_accounts
-- ----------------------------
BEGIN;
INSERT INTO `email_accounts` (`id`, `account_id`, `tenant_id`, `internal_app_id`, `name`, `type`, `provider`, `host`, `port`, `username`, `password`, `from_address`, `from_name`, `reply_to_address`, `is_ssl`, `is_active`, `daily_limit`, `monthly_limit`, `sent_today`, `sent_this_month`, `last_sent_at`, `test_status`, `test_message`, `config`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `version`, `is_system`) VALUES (1, 1, 1, 0, '系统账户', 5, 'Aliyun', '', 0, '', '', '<EMAIL>', '用户服务', '', 1, 1, 100, 3000, 0, 0, NULL, 0, '', '{\"domain\": \"mailservice.imymob.com\", \"region\": \"cn-hangzhou\", \"access_key_id\": \"LTAI5tP82zm8Q4oRJ65XDCDe\", \"access_key_secret\": \"******************************\"}', '2025-07-11 23:39:22.946', '2025-07-11 23:39:22.946', NULL, '', '', 1, 0);
COMMIT;

-- ----------------------------
-- Table structure for email_messages
-- ----------------------------
DROP TABLE IF EXISTS `email_messages`;
CREATE TABLE `email_messages` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `email_id` bigint NOT NULL,
  `tenant_id` bigint NOT NULL,
  `internal_app_id` bigint NOT NULL COMMENT '内部应用id',
  `template_id` varchar(100) DEFAULT NULL,
  `from_address` varchar(200) NOT NULL,
  `to_addresses` json NOT NULL,
  `cc_addresses` json DEFAULT NULL,
  `bcc_addresses` json DEFAULT NULL,
  `subject` varchar(500) NOT NULL,
  `html_content` text,
  `text_content` text,
  `variables` json DEFAULT NULL,
  `status` varchar(20) NOT NULL,
  `priority` bigint DEFAULT '2',
  `retry_count` bigint DEFAULT '0',
  `max_retries` bigint DEFAULT '3',
  `error_msg` text,
  `sent_at` datetime(3) DEFAULT NULL,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_email_messages_email_id` (`email_id`),
  KEY `idx_email_messages_tenant_id` (`tenant_id`),
  KEY `idx_email_messages_status` (`status`),
  KEY `idx_email_messages_deleted_at` (`deleted_at`),
  KEY `idx_tenant_status_created` (`tenant_id`,`status`,`created_at`),
  KEY `idx_tenant_sent_at` (`tenant_id`,`sent_at`)
) ENGINE=InnoDB AUTO_INCREMENT=4100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of email_messages
-- ----------------------------
BEGIN;
INSERT INTO `email_messages` (`id`, `email_id`, `tenant_id`, `internal_app_id`, `template_id`, `from_address`, `to_addresses`, `cc_addresses`, `bcc_addresses`, `subject`, `html_content`, `text_content`, `variables`, `status`, `priority`, `retry_count`, `max_retries`, `error_msg`, `sent_at`, `created_at`, `updated_at`, `deleted_at`) VALUES (1, 0, 2, 0, '4', '<EMAIL>', '[\"<EMAIL>\"]', NULL, NULL, '尊敬的用户：\n\n您的密码重置验证码为：{{verify_code}}\n\n验证码有效期为1440分钟。\n\n如果这不是您的操作，请忽略此邮件。', '密码重置验证码', '<!DOCTYPE html><html><body><p>尊敬的用户：</p><p>您的密码重置验证码为：<strong>{{verify_code}}</strong></p><p>验证码有效期为1440分钟。</p><p>如果这不是您的操作，请忽略此邮件。</p></body></html>', '{\"purpose\": \"密码重置\", \"token_type\": \"\\u0001\", \"tenant_code\": \"ilike\", \"tenant_name\": \"ilike\", \"verify_link\": \"https://your-domain.com/reset-password?token=2bb15ef89035e59f231c3aeedc2b4f95\", \"company_name\": \"ilike\", \"request_time\": \"2025-07-28 13:56:54\", \"target_email\": \"<EMAIL>\", \"support_email\": \"<EMAIL>\", \"expire_minutes\": \"1440\", \"verification_code\": \"2bb15ef89035e59f231c3aeedc2b4f95\"}', 'sent', 2, 0, 3, '', '2025-07-28 05:56:55.000', '2025-07-28 05:56:55.045', '2025-07-28 05:56:55.000', NULL);
INSERT INTO `email_messages` (`id`, `email_id`, `tenant_id`, `internal_app_id`, `template_id`, `from_address`, `to_addresses`, `cc_addresses`, `bcc_addresses`, `subject`, `html_content`, `text_content`, `variables`, `status`, `priority`, `retry_count`, `max_retries`, `error_msg`, `sent_at`, `created_at`, `updated_at`, `deleted_at`) VALUES (100, 100, 2, 0, '3', '<EMAIL>', '[\"<EMAIL>\"]', NULL, NULL, '尊敬的用户：\n\n您请求重置密码，请点击以下链接完成重置：\n{{reset_link}}\n\n链接有效期为{{expire_minutes}}分钟。\n\n如果这不是您的操作，请忽略此邮件。', '密码重置请求', '<!DOCTYPE html><html><body><p>尊敬的用户：</p><p>您请求重置密码，请点击以下链接完成重置：</p><p><a href=\"{{reset_link}}\">点击重置密码</a></p><p>链接有效期为{{expire_minutes}}分钟。</p><p>如果这不是您的操作，请忽略此邮件。</p></body></html>', '{\"link\": \"https://your-domain.com?token=71676d\", \"purpose\": \"密码重置\", \"expire_time\": \"24小时\"}', 'sent', 2, 0, 3, '', '2025-07-29 16:15:20.000', '2025-07-29 16:15:19.598', '2025-07-29 16:15:20.000', NULL);
INSERT INTO `email_messages` (`id`, `email_id`, `tenant_id`, `internal_app_id`, `template_id`, `from_address`, `to_addresses`, `cc_addresses`, `bcc_addresses`, `subject`, `html_content`, `text_content`, `variables`, `status`, `priority`, `retry_count`, `max_retries`, `error_msg`, `sent_at`, `created_at`, `updated_at`, `deleted_at`) VALUES (1099, 1099, 2, 0, '3', '<EMAIL>', '[\"<EMAIL>\"]', NULL, NULL, '', '密码重置请求', '<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>密码重置</title>\n    <style>\n        body {\n            font-family: \"Microsoft YaHei\", Arial, sans-serif;\n            line-height: 1.6;\n            color: #333;\n            margin: 0;\n            padding: 0;\n            background-color: #f5f5f5;\n        }\n        .container {\n            max-width: 600px;\n            margin: 0 auto;\n            background-color: #ffffff;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        .header {\n            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n            color: white;\n            padding: 30px;\n            text-align: center;\n        }\n        .header h1 {\n            margin: 0;\n            font-size: 24px;\n            font-weight: 300;\n        }\n        .content {\n            padding: 40px 30px;\n        }\n        .greeting {\n            font-size: 18px;\n            margin-bottom: 20px;\n            color: #2c3e50;\n        }\n        .main-text {\n            font-size: 16px;\n            margin-bottom: 25px;\n            line-height: 1.8;\n        }\n        .reset-button {\n            display: inline-block;\n            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n            color: white;\n            padding: 15px 30px;\n            text-decoration: none;\n            border-radius: 5px;\n            font-size: 16px;\n            font-weight: bold;\n            margin: 20px 0;\n            text-align: center;\n            box-shadow: 0 4px 15px rgba(255,107,107,0.3);\n            transition: all 0.3s ease;\n        }\n        .reset-button:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 6px 20px rgba(255,107,107,0.4);\n        }\n        .warning-message {\n            background-color: #fff3cd;\n            border: 1px solid #ffeaa7;\n            color: #856404;\n            padding: 15px;\n            border-radius: 4px;\n            margin: 20px 0;\n        }\n        .info-box {\n            background-color: #f8f9fa;\n            border-left: 4px solid #ff6b6b;\n            padding: 20px;\n            margin: 25px 0;\n            border-radius: 4px;\n        }\n        .info-box h3 {\n            margin: 0 0 15px 0;\n            color: #ff6b6b;\n            font-size: 16px;\n        }\n        .info-item {\n            margin: 8px 0;\n            font-size: 14px;\n        }\n        .info-label {\n            font-weight: bold;\n            color: #555;\n            display: inline-block;\n            width: 100px;\n        }\n        .footer {\n            background-color: #f8f9fa;\n            padding: 20px 30px;\n            text-align: center;\n            border-top: 1px solid #e9ecef;\n        }\n        .footer-text {\n            font-size: 12px;\n            color: #6c757d;\n            line-height: 1.5;\n        }\n        .divider {\n            border-top: 1px solid #e9ecef;\n            margin: 20px 0;\n        }\n        @media only screen and (max-width: 600px) {\n            .container {\n                margin: 0;\n                box-shadow: none;\n            }\n            .header, .content, .footer {\n                padding: 20px;\n            }\n            .reset-button {\n                display: block;\n                margin: 20px auto;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>🔐 密码重置请求</h1>\n        </div>\n        <div class=\"content\">\n            <div class=\"greeting\">尊敬的用户，您好！</div>\n            <div class=\"main-text\">\n                我们收到了您的密码重置请求。为了确保您的账户安全，请点击下面的按钮重置您的密码。\n            </div>\n            <div style=\"text-align: center;\">\n                <a href=\"https://your-domain.com?token=383772\" class=\"reset-button\">🔑 立即重置密码</a>\n            </div>\n            <div class=\"warning-message\">\n                <strong>⚠️ 重要提醒</strong><br>\n                此重置链接将在 <strong>24小时</strong> 后失效，请及时完成密码重置。\n            </div>\n            <div class=\"info-box\">\n                <h3>📋 重置说明</h3>\n                <div class=\"info-item\">\n                    <span class=\"info-label\">重置链接:</span>\n                    <span style=\"word-break: break-all; color: #ff6b6b;\">https://your-domain.com?token=383772</span>\n                </div>\n                <div class=\"info-item\">\n                    <span class=\"info-label\">过期时间:</span>\n                    <span>24小时</span>\n                </div>\n            </div>\n            <div class=\"main-text\">\n                如果按钮无法点击，请复制上面的链接到浏览器地址栏中访问。<br>\n                如果您没有发起密码重置请求，请忽略此邮件，您的密码将保持不变。\n            </div>\n        </div>\n        <div class=\"footer\">\n            <div class=\"footer-text\">\n                <p>本邮件由系统自动发送，请勿回复。</p>\n                <p>为了您的账户安全，请妥善保管您的密码。</p>\n                <div class=\"divider\"></div>\n                <p style=\"font-size: 11px; color: #999;\">发送时间: 10:45:14 | 系统安全中心</p>\n            </div>\n        </div>\n    </div>\n</body>\n</html>', '{\"link\": \"https://your-domain.com?token=383772\", \"purpose\": \"密码重置\", \"expire_time\": \"24小时\"}', 'sent', 2, 0, 3, '', '2025-07-30 02:45:15.000', '2025-07-30 02:45:14.579', '2025-07-30 02:45:15.000', NULL);
INSERT INTO `email_messages` (`id`, `email_id`, `tenant_id`, `internal_app_id`, `template_id`, `from_address`, `to_addresses`, `cc_addresses`, `bcc_addresses`, `subject`, `html_content`, `text_content`, `variables`, `status`, `priority`, `retry_count`, `max_retries`, `error_msg`, `sent_at`, `created_at`, `updated_at`, `deleted_at`) VALUES (2099, 2099, 2, 0, '3', '<EMAIL>', '[\"<EMAIL>\"]', NULL, NULL, '密码重置请求', '<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>密码重置</title>\n    <style>\n        body {\n            font-family: \"Microsoft YaHei\", Arial, sans-serif;\n            line-height: 1.6;\n            color: #333;\n            margin: 0;\n            padding: 0;\n            background-color: #f5f5f5;\n        }\n        .container {\n            max-width: 600px;\n            margin: 0 auto;\n            background-color: #ffffff;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        .header {\n            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n            color: white;\n            padding: 30px;\n            text-align: center;\n        }\n        .header h1 {\n            margin: 0;\n            font-size: 24px;\n            font-weight: 300;\n        }\n        .content {\n            padding: 40px 30px;\n        }\n        .greeting {\n            font-size: 18px;\n            margin-bottom: 20px;\n            color: #2c3e50;\n        }\n        .main-text {\n            font-size: 16px;\n            margin-bottom: 25px;\n            line-height: 1.8;\n        }\n        .reset-button {\n            display: inline-block;\n            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n            color: white;\n            padding: 15px 30px;\n            text-decoration: none;\n            border-radius: 5px;\n            font-size: 16px;\n            font-weight: bold;\n            margin: 20px 0;\n            text-align: center;\n            box-shadow: 0 4px 15px rgba(255,107,107,0.3);\n            transition: all 0.3s ease;\n        }\n        .reset-button:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 6px 20px rgba(255,107,107,0.4);\n        }\n        .warning-message {\n            background-color: #fff3cd;\n            border: 1px solid #ffeaa7;\n            color: #856404;\n            padding: 15px;\n            border-radius: 4px;\n            margin: 20px 0;\n        }\n        .info-box {\n            background-color: #f8f9fa;\n            border-left: 4px solid #ff6b6b;\n            padding: 20px;\n            margin: 25px 0;\n            border-radius: 4px;\n        }\n        .info-box h3 {\n            margin: 0 0 15px 0;\n            color: #ff6b6b;\n            font-size: 16px;\n        }\n        .info-item {\n            margin: 8px 0;\n            font-size: 14px;\n        }\n        .info-label {\n            font-weight: bold;\n            color: #555;\n            display: inline-block;\n            width: 100px;\n        }\n        .footer {\n            background-color: #f8f9fa;\n            padding: 20px 30px;\n            text-align: center;\n            border-top: 1px solid #e9ecef;\n        }\n        .footer-text {\n            font-size: 12px;\n            color: #6c757d;\n            line-height: 1.5;\n        }\n        .divider {\n            border-top: 1px solid #e9ecef;\n            margin: 20px 0;\n        }\n        @media only screen and (max-width: 600px) {\n            .container {\n                margin: 0;\n                box-shadow: none;\n            }\n            .header, .content, .footer {\n                padding: 20px;\n            }\n            .reset-button {\n                display: block;\n                margin: 20px auto;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>🔐 密码重置请求</h1>\n        </div>\n        <div class=\"content\">\n            <div class=\"greeting\">尊敬的用户，您好！</div>\n            <div class=\"main-text\">\n                我们收到了您的密码重置请求。为了确保您的账户安全，请点击下面的按钮重置您的密码。\n            </div>\n            <div style=\"text-align: center;\">\n                <a href=\"https://your-domain.com?token=17d0bdce211ed3b5d590383d56a23d4d1019423843616ee51413f62b436aefa5\" class=\"reset-button\">🔑 立即重置密码</a>\n            </div>\n            <div class=\"warning-message\">\n                <strong>⚠️ 重要提醒</strong><br>\n                此重置链接将在 <strong>24小时</strong> 后失效，请及时完成密码重置。\n            </div>\n            <div class=\"info-box\">\n                <h3>📋 重置说明</h3>\n                <div class=\"info-item\">\n                    <span class=\"info-label\">重置链接:</span>\n                    <span style=\"word-break: break-all; color: #ff6b6b;\">https://your-domain.com?token=17d0bdce211ed3b5d590383d56a23d4d1019423843616ee51413f62b436aefa5</span>\n                </div>\n                <div class=\"info-item\">\n                    <span class=\"info-label\">过期时间:</span>\n                    <span>24小时</span>\n                </div>\n            </div>\n            <div class=\"main-text\">\n                如果按钮无法点击，请复制上面的链接到浏览器地址栏中访问。<br>\n                如果您没有发起密码重置请求，请忽略此邮件，您的密码将保持不变。\n            </div>\n        </div>\n        <div class=\"footer\">\n            <div class=\"footer-text\">\n                <p>本邮件由系统自动发送，请勿回复。</p>\n                <p>为了您的账户安全，请妥善保管您的密码。</p>\n                <div class=\"divider\"></div>\n                <p style=\"font-size: 11px; color: #999;\">发送时间: 11:13:28 | 系统安全中心</p>\n            </div>\n        </div>\n    </div>\n</body>\n</html>', '', '{\"link\": \"https://your-domain.com?token=17d0bdce211ed3b5d590383d56a23d4d1019423843616ee51413f62b436aefa5\", \"purpose\": \"密码重置\", \"expire_time\": \"24小时\"}', 'failed', 2, 0, 3, '', NULL, '2025-07-30 03:13:29.062', '2025-07-30 03:13:29.000', NULL);
INSERT INTO `email_messages` (`id`, `email_id`, `tenant_id`, `internal_app_id`, `template_id`, `from_address`, `to_addresses`, `cc_addresses`, `bcc_addresses`, `subject`, `html_content`, `text_content`, `variables`, `status`, `priority`, `retry_count`, `max_retries`, `error_msg`, `sent_at`, `created_at`, `updated_at`, `deleted_at`) VALUES (2100, 2100, 2, 0, '3', '<EMAIL>', '[\"<EMAIL>\"]', NULL, NULL, '密码重置请求', '<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>密码重置</title>\n    <style>\n        body {\n            font-family: \"Microsoft YaHei\", Arial, sans-serif;\n            line-height: 1.6;\n            color: #333;\n            margin: 0;\n            padding: 0;\n            background-color: #f5f5f5;\n        }\n        .container {\n            max-width: 600px;\n            margin: 0 auto;\n            background-color: #ffffff;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        .header {\n            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n            color: white;\n            padding: 30px;\n            text-align: center;\n        }\n        .header h1 {\n            margin: 0;\n            font-size: 24px;\n            font-weight: 300;\n        }\n        .content {\n            padding: 40px 30px;\n        }\n        .greeting {\n            font-size: 18px;\n            margin-bottom: 20px;\n            color: #2c3e50;\n        }\n        .main-text {\n            font-size: 16px;\n            margin-bottom: 25px;\n            line-height: 1.8;\n        }\n        .reset-button {\n            display: inline-block;\n            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n            color: white;\n            padding: 15px 30px;\n            text-decoration: none;\n            border-radius: 5px;\n            font-size: 16px;\n            font-weight: bold;\n            margin: 20px 0;\n            text-align: center;\n            box-shadow: 0 4px 15px rgba(255,107,107,0.3);\n            transition: all 0.3s ease;\n        }\n        .reset-button:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 6px 20px rgba(255,107,107,0.4);\n        }\n        .warning-message {\n            background-color: #fff3cd;\n            border: 1px solid #ffeaa7;\n            color: #856404;\n            padding: 15px;\n            border-radius: 4px;\n            margin: 20px 0;\n        }\n        .info-box {\n            background-color: #f8f9fa;\n            border-left: 4px solid #ff6b6b;\n            padding: 20px;\n            margin: 25px 0;\n            border-radius: 4px;\n        }\n        .info-box h3 {\n            margin: 0 0 15px 0;\n            color: #ff6b6b;\n            font-size: 16px;\n        }\n        .info-item {\n            margin: 8px 0;\n            font-size: 14px;\n        }\n        .info-label {\n            font-weight: bold;\n            color: #555;\n            display: inline-block;\n            width: 100px;\n        }\n        .footer {\n            background-color: #f8f9fa;\n            padding: 20px 30px;\n            text-align: center;\n            border-top: 1px solid #e9ecef;\n        }\n        .footer-text {\n            font-size: 12px;\n            color: #6c757d;\n            line-height: 1.5;\n        }\n        .divider {\n            border-top: 1px solid #e9ecef;\n            margin: 20px 0;\n        }\n        @media only screen and (max-width: 600px) {\n            .container {\n                margin: 0;\n                box-shadow: none;\n            }\n            .header, .content, .footer {\n                padding: 20px;\n            }\n            .reset-button {\n                display: block;\n                margin: 20px auto;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>🔐 密码重置请求</h1>\n        </div>\n        <div class=\"content\">\n            <div class=\"greeting\">尊敬的用户，您好！</div>\n            <div class=\"main-text\">\n                我们收到了您的密码重置请求。为了确保您的账户安全，请点击下面的按钮重置您的密码。\n            </div>\n            <div style=\"text-align: center;\">\n                <a href=\"https://your-domain.com?token=dc8ae4d574b229c8514d24ac00b37cb3dbb2be36703dd5ca48b895b7c63ab144\" class=\"reset-button\">🔑 立即重置密码</a>\n            </div>\n            <div class=\"warning-message\">\n                <strong>⚠️ 重要提醒</strong><br>\n                此重置链接将在 <strong>24小时</strong> 后失效，请及时完成密码重置。\n            </div>\n            <div class=\"info-box\">\n                <h3>📋 重置说明</h3>\n                <div class=\"info-item\">\n                    <span class=\"info-label\">重置链接:</span>\n                    <span style=\"word-break: break-all; color: #ff6b6b;\">https://your-domain.com?token=dc8ae4d574b229c8514d24ac00b37cb3dbb2be36703dd5ca48b895b7c63ab144</span>\n                </div>\n                <div class=\"info-item\">\n                    <span class=\"info-label\">过期时间:</span>\n                    <span>24小时</span>\n                </div>\n            </div>\n            <div class=\"main-text\">\n                如果按钮无法点击，请复制上面的链接到浏览器地址栏中访问。<br>\n                如果您没有发起密码重置请求，请忽略此邮件，您的密码将保持不变。\n            </div>\n        </div>\n        <div class=\"footer\">\n            <div class=\"footer-text\">\n                <p>本邮件由系统自动发送，请勿回复。</p>\n                <p>为了您的账户安全，请妥善保管您的密码。</p>\n                <div class=\"divider\"></div>\n                <p style=\"font-size: 11px; color: #999;\">发送时间: 11:14:52 | 系统安全中心</p>\n            </div>\n        </div>\n    </div>\n</body>\n</html>', '', '{\"link\": \"https://your-domain.com?token=dc8ae4d574b229c8514d24ac00b37cb3dbb2be36703dd5ca48b895b7c63ab144\", \"purpose\": \"密码重置\", \"expire_time\": \"24小时\"}', 'failed', 2, 0, 3, '', NULL, '2025-07-30 03:14:53.003', '2025-07-30 03:14:53.000', NULL);
INSERT INTO `email_messages` (`id`, `email_id`, `tenant_id`, `internal_app_id`, `template_id`, `from_address`, `to_addresses`, `cc_addresses`, `bcc_addresses`, `subject`, `html_content`, `text_content`, `variables`, `status`, `priority`, `retry_count`, `max_retries`, `error_msg`, `sent_at`, `created_at`, `updated_at`, `deleted_at`) VALUES (3099, 3099, 2, 0, '3', '<EMAIL>', '[\"<EMAIL>\"]', NULL, NULL, '密码重置请求', '<!DOCTYPE html>\n<html lang=\"zh-CN\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n    <title>密码重置</title>\n    <style>\n        body {\n            font-family: \"Microsoft YaHei\", Arial, sans-serif;\n            line-height: 1.6;\n            color: #333;\n            margin: 0;\n            padding: 0;\n            background-color: #f5f5f5;\n        }\n        .container {\n            max-width: 600px;\n            margin: 0 auto;\n            background-color: #ffffff;\n            box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n        }\n        .header {\n            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n            color: white;\n            padding: 30px;\n            text-align: center;\n        }\n        .header h1 {\n            margin: 0;\n            font-size: 24px;\n            font-weight: 300;\n        }\n        .content {\n            padding: 40px 30px;\n        }\n        .greeting {\n            font-size: 18px;\n            margin-bottom: 20px;\n            color: #2c3e50;\n        }\n        .main-text {\n            font-size: 16px;\n            margin-bottom: 25px;\n            line-height: 1.8;\n        }\n        .reset-button {\n            display: inline-block;\n            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n            color: white;\n            padding: 15px 30px;\n            text-decoration: none;\n            border-radius: 5px;\n            font-size: 16px;\n            font-weight: bold;\n            margin: 20px 0;\n            text-align: center;\n            box-shadow: 0 4px 15px rgba(255,107,107,0.3);\n            transition: all 0.3s ease;\n        }\n        .reset-button:hover {\n            transform: translateY(-2px);\n            box-shadow: 0 6px 20px rgba(255,107,107,0.4);\n        }\n        .warning-message {\n            background-color: #fff3cd;\n            border: 1px solid #ffeaa7;\n            color: #856404;\n            padding: 15px;\n            border-radius: 4px;\n            margin: 20px 0;\n        }\n        .info-box {\n            background-color: #f8f9fa;\n            border-left: 4px solid #ff6b6b;\n            padding: 20px;\n            margin: 25px 0;\n            border-radius: 4px;\n        }\n        .info-box h3 {\n            margin: 0 0 15px 0;\n            color: #ff6b6b;\n            font-size: 16px;\n        }\n        .info-item {\n            margin: 8px 0;\n            font-size: 14px;\n        }\n        .info-label {\n            font-weight: bold;\n            color: #555;\n            display: inline-block;\n            width: 100px;\n        }\n        .footer {\n            background-color: #f8f9fa;\n            padding: 20px 30px;\n            text-align: center;\n            border-top: 1px solid #e9ecef;\n        }\n        .footer-text {\n            font-size: 12px;\n            color: #6c757d;\n            line-height: 1.5;\n        }\n        .divider {\n            border-top: 1px solid #e9ecef;\n            margin: 20px 0;\n        }\n        @media only screen and (max-width: 600px) {\n            .container {\n                margin: 0;\n                box-shadow: none;\n            }\n            .header, .content, .footer {\n                padding: 20px;\n            }\n            .reset-button {\n                display: block;\n                margin: 20px auto;\n            }\n        }\n    </style>\n</head>\n<body>\n    <div class=\"container\">\n        <div class=\"header\">\n            <h1>🔐 密码重置请求</h1>\n        </div>\n        <div class=\"content\">\n            <div class=\"greeting\">尊敬的用户，您好！</div>\n            <div class=\"main-text\">\n                我们收到了您的密码重置请求。为了确保您的账户安全，请点击下面的按钮重置您的密码。\n            </div>\n            <div style=\"text-align: center;\">\n                <a href=\"https://your-domain.com?token=fc550fc32d36d4d86d51225832d24b383006f1a88c268e27a571653797344b0b\" class=\"reset-button\">🔑 立即重置密码</a>\n            </div>\n            <div class=\"warning-message\">\n                <strong>⚠️ 重要提醒</strong><br>\n                此重置链接将在 <strong>24小时</strong> 后失效，请及时完成密码重置。\n            </div>\n            <div class=\"info-box\">\n                <h3>📋 重置说明</h3>\n                <div class=\"info-item\">\n                    <span class=\"info-label\">重置链接:</span>\n                    <span style=\"word-break: break-all; color: #ff6b6b;\">https://your-domain.com?token=fc550fc32d36d4d86d51225832d24b383006f1a88c268e27a571653797344b0b</span>\n                </div>\n                <div class=\"info-item\">\n                    <span class=\"info-label\">过期时间:</span>\n                    <span>24小时</span>\n                </div>\n            </div>\n            <div class=\"main-text\">\n                如果按钮无法点击，请复制上面的链接到浏览器地址栏中访问。<br>\n                如果您没有发起密码重置请求，请忽略此邮件，您的密码将保持不变。\n            </div>\n        </div>\n        <div class=\"footer\">\n            <div class=\"footer-text\">\n                <p>本邮件由系统自动发送，请勿回复。</p>\n                <p>为了您的账户安全，请妥善保管您的密码。</p>\n                <div class=\"divider\"></div>\n                <p style=\"font-size: 11px; color: #999;\">发送时间: 11:32:40 | 系统安全中心</p>\n            </div>\n        </div>\n    </div>\n</body>\n</html>', '', '{\"link\": \"https://your-domain.com?token=fc550fc32d36d4d86d51225832d24b383006f1a88c268e27a571653797344b0b\", \"purpose\": \"密码重置\", \"expire_time\": \"24小时\"}', 'sent', 2, 0, 3, '', '2025-07-30 03:32:41.000', '2025-07-30 03:32:40.746', '2025-07-30 03:32:41.000', NULL);
INSERT INTO `email_messages` (`id`, `email_id`, `tenant_id`, `internal_app_id`, `template_id`, `from_address`, `to_addresses`, `cc_addresses`, `bcc_addresses`, `subject`, `html_content`, `text_content`, `variables`, `status`, `priority`, `retry_count`, `max_retries`, `error_msg`, `sent_at`, `created_at`, `updated_at`, `deleted_at`) VALUES (4099, 4099, 2, 0, '3', '<EMAIL>', '[\"<EMAIL>\"]', NULL, NULL, '密码重置请求', '<p>尊敬的用户，您好！</p><p>我们收到了您的密码重置请求。请点击下方按钮完成密码重置操作。</p><p><a href=\"http://localhost:8084?token=7570b9a49b5a37ea7fd6b59ff4dee5ac91f68a9bc1fe3a02c5ce1eaf3818ad4a\">重置密码&nbsp;</a></p><p>此重置链接将在 24小时 后失效，请及时完成操作。</p><p>重置链接:</p><p>http://localhost:8084?token=7570b9a49b5a37ea7fd6b59ff4dee5ac91f68a9bc1fe3a02c5ce1eaf3818ad4a</p><p>过期时间: 24小时</p><p>请求时间: 23:52:02</p><p>如果您没有发起密码重置请求，请忽略此邮件。为了账户安全，请勿将重置链接分享给他人。</p><p>本邮件由系统自动发送，请勿回复</p><p> | 发送时间: 23:52:02</p>', '', '{\"link\": \"http://localhost:8084?token=7570b9a49b5a37ea7fd6b59ff4dee5ac91f68a9bc1fe3a02c5ce1eaf3818ad4a\", \"purpose\": \"密码重置\", \"expire_time\": \"24小时\"}', 'sent', 2, 0, 3, '', '2025-08-01 15:52:03.000', '2025-08-01 15:52:02.612', '2025-08-01 15:52:03.000', NULL);
COMMIT;

-- ----------------------------
-- Table structure for email_templates
-- ----------------------------
DROP TABLE IF EXISTS `email_templates`;
CREATE TABLE `email_templates` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint unsigned NOT NULL,
  `internal_app_id` bigint NOT NULL,
  `template_code` varchar(50) NOT NULL COMMENT '模板代码',
  `account_id` bigint unsigned NOT NULL COMMENT '关联的发送账户ID',
  `name` varchar(100) NOT NULL,
  `type` tinyint unsigned NOT NULL,
  `subject` varchar(200) NOT NULL,
  `html_content` text,
  `plain_text_content` text,
  `variables` json DEFAULT NULL COMMENT '模板变量定义：{"variable_name": {"type": "string", "required": true, "description": "描述"}}',
  `rate_limit_per_minute` int unsigned DEFAULT '60' COMMENT '每分钟发送限制',
  `rate_limit_per_hour` int unsigned DEFAULT '1000' COMMENT '每小时发送限制',
  `rate_limit_per_day` int unsigned DEFAULT '10000' COMMENT '每日发送限制',
  `thumbnail_url` varchar(500) DEFAULT NULL,
  `is_responsive` tinyint(1) DEFAULT '0',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `created_by` bigint unsigned DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `version` bigint unsigned NOT NULL DEFAULT '1',
  `statistics` json DEFAULT NULL,
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否为系统内置模板，TRUE表示系统内置，需要超级管理员权限',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '模板状态：1草稿，2已发布，3已停用，4已删除',
  `description` varchar(255) DEFAULT NULL COMMENT '描述信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tenant_template_code` (`tenant_id`,`template_code`),
  KEY `idx_email_templates_tenant_id` (`tenant_id`),
  KEY `idx_email_templates_deleted_at` (`deleted_at`),
  KEY `idx_account` (`account_id`),
  KEY `idx_email_templates_is_system` (`tenant_id`,`is_system`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of email_templates
-- ----------------------------
BEGIN;
INSERT INTO `email_templates` (`id`, `tenant_id`, `internal_app_id`, `template_code`, `account_id`, `name`, `type`, `subject`, `html_content`, `plain_text_content`, `variables`, `rate_limit_per_minute`, `rate_limit_per_hour`, `rate_limit_per_day`, `thumbnail_url`, `is_responsive`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `version`, `statistics`, `is_system`, `status`, `description`) VALUES (3, 1, 0, 'password_reset', 1, '密码重置链接模板', 1, '密码重置请求', '<p>尊敬的用户，您好！</p><p>我们收到了您的密码重置请求。请点击下方按钮完成密码重置操作。</p><p><a href=\"{{link}}\">重置密码&nbsp;</a></p><p>此重置链接将在 {{expire_time}} 后失效，请及时完成操作。</p><p>重置链接:</p><p>{{link}}</p><p>过期时间: {{expire_time}}</p><p>请求时间: {{current_time}}</p><p>如果您没有发起密码重置请求，请忽略此邮件。为了账户安全，请勿将重置链接分享给他人。</p><p>本邮件由系统自动发送，请勿回复</p><p>{{company_name}} | 发送时间: {{current_time}}</p>', '', '{\"link\": {\"type\": \"string\", \"label\": \"重置密码链接\", \"required\": false, \"description\": \"密码重置链接\"}, \"expire_time\": {\"type\": \"string\", \"label\": \"失效时间\", \"required\": false, \"description\": \"重置链接失效时间\"}}', 60, 1000, 10000, '', 0, '2025-07-09 23:55:16.000', '2025-07-30 04:07:07.323', NULL, NULL, 1001, 14, '{}', 0, 2, NULL);
INSERT INTO `email_templates` (`id`, `tenant_id`, `internal_app_id`, `template_code`, `account_id`, `name`, `type`, `subject`, `html_content`, `plain_text_content`, `variables`, `rate_limit_per_minute`, `rate_limit_per_hour`, `rate_limit_per_day`, `thumbnail_url`, `is_responsive`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `version`, `statistics`, `is_system`, `status`, `description`) VALUES (6, 1, 0, 'account_verification', 1, '邮件账户验证模板', 1, '【服务验证】{{company_name}}-邮件服务配置确认', '<h2>📧 邮件服务配置验证</h2><p>尊敬的用户，您好！</p><p>感谢您使用我们的邮件服务平台。本邮件用于验证您的邮件服务配置是否正常工作，确保后续的业务邮件、系统通知、客户沟通等重要信息能够及时送达。</p><p><strong>✅ 验证成功</strong><br>如果您收到这封邮件，说明您的邮件服务配置已正确设置，可以正常发送和接收邮件。</p><p>发送时间: {{current_time}}</p>', '尊敬的用户，您好！\n\n感谢您使用我们的邮件服务平台。本邮件用于验证您的邮件服务配置是否正常工作，确保后续的业务邮件、系统通知、客户沟通等重要信息能够及时送达。\n\n✅ 验证成功\n如果您收到这封邮件，说明您的邮件服务配置已正确设置，可以正常发送和接收邮件。\n\n📋 服务配置信息\n服务名称: {{account_name}}\n服务类型: {{account_type}}\n服务提供商: {{provider}}\n发送地址: {{from_address}}\n验证时间: {{current_time}}\n\n您的邮件服务现已准备就绪，可以开始发送各类业务邮件。如有任何问题或需要技术支持，请随时联系我们的客服团队。\n\n---\n本邮件由系统自动发送，请勿回复。\n感谢您对我们服务的信任与支持！\n\n发送时间: {{current_time}} | 邮件ID: {{account_id}}', '{\"current_time\": {\"type\": \"string\", \"label\": \"\", \"required\": true, \"description\": \"当前验证时间\"}}', 60, 1000, 10000, NULL, 1, '2025-07-12 16:04:50.000', '2025-07-29 10:24:32.935', NULL, NULL, NULL, 6, '{}', 1, 2, NULL);
INSERT INTO `email_templates` (`id`, `tenant_id`, `internal_app_id`, `template_code`, `account_id`, `name`, `type`, `subject`, `html_content`, `plain_text_content`, `variables`, `rate_limit_per_minute`, `rate_limit_per_hour`, `rate_limit_per_day`, `thumbnail_url`, `is_responsive`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `version`, `statistics`, `is_system`, `status`, `description`) VALUES (7, 1, 0, 'account_activation', 1, '注册后激活邮件', 1, '【{{company_name}}】欢迎！请激活您的账户', '<h2>🎉 欢迎加入我们</h2><p>您的账户激活邮件</p><p>尊敬的用户，您好！</p><p>感谢您注册我们的平台！为了确保您的账户安全，请点击下面的按钮激活您的账户。 激活后您将可以享受我们提供的所有服务。</p><p><strong>🔐 安全激活</strong><a href=\"{{link}}\">✨ 立即激活账户&nbsp;</a></p><p>点击按钮即可完成账户激活</p><p>重要提醒</p><p>此激活链接将在 <strong>{{expire_time}}</strong> 后失效，请及时激活您的账户。 为了您的账户安全，请勿将激活链接分享给他人。</p><p>如果按钮无法点击，请复制以下链接到浏览器地址栏中访问：</p><p>{{link}}</p><p>如果您没有注册我们的平台，请忽略此邮件。<br>如有任何问题，请联系我们的客服团队。</p><p>本邮件由系统自动发送，请勿回复。</p><p>感谢您对我们服务的信任与支持！</p>', '', '{\"link\": {\"type\": \"string\", \"label\": \"link\", \"required\": false, \"description\": \"激活链接\"}, \"expire_time\": {\"type\": \"string\", \"label\": \"失效时间\", \"required\": false, \"description\": \"激活链接失效时间\"}, \"verify_code\": {\"type\": \"string\", \"label\": \"verify_code\", \"required\": false, \"description\": \"失效时间\"}}', 60, 1000, 10000, '', 0, '2025-07-26 04:59:52.685', '2025-07-29 06:05:36.528', NULL, 0, 0, 27, '{}', 0, 2, '');
COMMIT;

-- ----------------------------
-- Table structure for tenant_configs
-- ----------------------------
DROP TABLE IF EXISTS `tenant_configs`;
CREATE TABLE `tenant_configs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tenant_id` bigint unsigned NOT NULL,
  `internal_app_id` bigint NOT NULL,
  `email_config` json DEFAULT NULL,
  `quotas` json DEFAULT NULL,
  `settings` json DEFAULT NULL,
  `statistics` json DEFAULT NULL,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `created_by` bigint unsigned DEFAULT NULL,
  `updated_by` bigint unsigned DEFAULT NULL,
  `version` int unsigned DEFAULT '1',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_tenant_configs_tenant_id` (`tenant_id`),
  KEY `idx_tenant_configs_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of tenant_configs
-- ----------------------------
BEGIN;
INSERT INTO `tenant_configs` (`id`, `tenant_id`, `internal_app_id`, `email_config`, `quotas`, `settings`, `statistics`, `created_at`, `updated_at`, `deleted_at`, `created_by`, `updated_by`, `version`) VALUES (1, 1, 0, '{\"reply_to\": \"<EMAIL>\", \"from_name\": \"测试公司\", \"from_address\": \"<EMAIL>\", \"smtp_settings\": {\"host\": \"smtp.gmail.com\", \"port\": 587, \"security\": \"tls\", \"username\": \"<EMAIL>\", \"is_verified\": false}}', '{\"used_emails\": 0, \"used_storage\": 0, \"storage_limit\": 10737418240, \"monthly_emails\": 10000, \"used_api_calls\": 0, \"max_subscribers\": 5000, \"used_subscribers\": 0, \"api_calls_per_hour\": 1000}', '{\"currency\": \"CNY\", \"language\": \"zh-CN\", \"timezone\": \"Asia/Shanghai\", \"track_opens\": true, \"track_clicks\": true, \"bounce_threshold\": 3, \"track_unsubscribes\": true, \"auto_cleanup_bounces\": true}', '{\"last_activity\": \"2025-06-29T22:46:21.711551+08:00\", \"total_campaigns\": 0, \"total_templates\": 0, \"total_subscribers\": 0}', '2025-06-29 22:46:21.712', '2025-06-29 22:46:28.299', NULL, NULL, NULL, 3);
COMMIT;

-- ----------------------------
-- Table structure for email_providers
-- ----------------------------
DROP TABLE IF EXISTS `email_providers`;
CREATE TABLE `email_providers` (
  `id` bigint unsigned NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '唯一名称标识，如 gmail/outlook/qq/163/aliyun/smtp',
  `display_name` varchar(100) NOT NULL COMMENT '展示名称',
  `icon_url` varchar(255) DEFAULT NULL COMMENT '图标URL',

  `smtp_host` varchar(150) DEFAULT NULL,
  `smtp_port` int DEFAULT 0,

  `imap_host` varchar(150) DEFAULT NULL,
  `imap_port` int DEFAULT 0,

  `pop3_host` varchar(150) DEFAULT NULL,
  `pop3_port` int DEFAULT 0,

  `use_ssl` tinyint(1) DEFAULT 1,
  `enabled` tinyint(1) DEFAULT 1,
  `sort_order` int NOT NULL DEFAULT 0,

  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_email_providers_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- ----------------------------
-- Records of email_providers (seed)
-- ----------------------------
BEGIN;
INSERT INTO `email_providers` (`id`,`name`,`display_name`,`icon_url`,`smtp_host`,`smtp_port`,`imap_host`,`imap_port`,`pop3_host`,`pop3_port`,`use_ssl`,`enabled`,`created_at`,`updated_at`) VALUES
  (1,'gmail','Gmail','/assets/email/providers/gmail.png','smtp.gmail.com',587,'imap.gmail.com',993,'pop.gmail.com',995,1,1,NOW(),NOW()),
  (2,'outlook','Outlook','/assets/email/providers/outlook.png','smtp.office365.com',587,'outlook.office365.com',993,'outlook.office365.com',995,1,1,NOW(),NOW()),
  (3,'qq','QQ邮箱','/assets/email/providers/qq.png','smtp.qq.com',465,'imap.qq.com',993,'pop.qq.com',995,1,1,NOW(),NOW()),
  (4,'163','163邮箱','/assets/email/providers/163.png','smtp.163.com',465,'imap.163.com',993,'pop.163.com',995,1,1,NOW(),NOW()),
  (5,'aliyun','阿里云邮件推送','/assets/email/providers/aliyun.png','smtpdm.aliyun.com',465,NULL,0,NULL,0,1,1,NOW(),NOW()),
  (6,'smtp','其他服务商','/assets/email/providers/smtp.png',NULL,0,NULL,0,NULL,0,1,1,NOW(),NOW());
COMMIT;

-- Initialize icon_url to new SVG asset paths (frontend/public/assets/email-providers)
UPDATE `email_providers`
SET `icon_url` = CASE LOWER(`name`)
  WHEN 'gmail'   THEN '/assets/email-providers/gmail.svg'
  WHEN 'outlook' THEN '/assets/email-providers/outlook.svg'
  WHEN 'qq'      THEN '/assets/email-providers/qq.svg'
  WHEN '163'     THEN '/assets/email-providers/163.svg'
  WHEN 'aliyun'  THEN '/assets/email-providers/aliyun.svg'
  WHEN 'smtp'    THEN '/assets/email-providers/smtp.svg'
  ELSE `icon_url`
END;

-- Initialize sort order (lower value appears first)
UPDATE `email_providers`
SET `sort_order` = CASE LOWER(`name`)
  WHEN 'aliyun'  THEN 10
  WHEN 'gmail'   THEN 20
  WHEN 'outlook' THEN 30
  WHEN 'qq'      THEN 40
  WHEN '163'     THEN 50
  WHEN 'smtp'    THEN 90
  ELSE 80
END;

SET FOREIGN_KEY_CHECKS = 1;
