package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gitee.com/heiyee/platforms/email/internal/domain/email/entity"
	"gitee.com/heiyee/platforms/email/internal/infrastructure/permission"
	"gitee.com/heiyee/platforms/email/internal/infrastructure/persistence/model"

	"gorm.io/gorm"
)

// EmailAccountRepositoryImpl 邮件账号仓储实现
type EmailAccountRepositoryImpl struct {
	db                *gorm.DB
	permissionChecker *permission.PermissionChecker
}

// NewEmailAccountRepositoryImpl 创建邮件账号仓储实现
func NewEmailAccountRepositoryImpl(db *gorm.DB) *EmailAccountRepositoryImpl {
	return &EmailAccountRepositoryImpl{
		db:                db,
		permissionChecker: permission.NewPermissionChecker(), // 使用延迟初始化
	}
}

// Save 保存邮件账号（自动权限检查）
func (r *EmailAccountRepositoryImpl) Save(ctx context.Context, account *entity.EmailAccount) error {
	// 检查系统资源权限
	if err := r.permissionChecker.CheckSystemResourcePermissionFromContext(ctx, account.IsSystem); err != nil {
		return fmt.Errorf("权限检查失败: %w", err)
	}

	model := r.toModel(account)

	if err := r.db.WithContext(ctx).Create(model).Error; err != nil {
		return fmt.Errorf("failed to create email account: %w", err)
	}

	// 设置生成的ID
	account.ID = int64(model.ID)
	return nil
}

// FindByID 根据ID查找邮件账号（数据库自增ID，仅用于编辑配置）
func (r *EmailAccountRepositoryImpl) FindByID(ctx context.Context, id int64) (*entity.EmailAccount, error) {
	var model model.EmailAccountModel

	if err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, entity.ErrAccountNotFound
		}
		return nil, fmt.Errorf("failed to find email account by id: %w", err)
	}

	account := r.toEntity(&model)

	// 检查系统资源权限
	if err := r.permissionChecker.CheckSystemResourcePermissionFromContext(ctx, account.IsSystem); err != nil {
		return nil, fmt.Errorf("权限检查失败: %w", err)
	}

	return account, nil
}

// FindByAccountID 根据账号ID查找邮件账号（分布式ID，用于业务逻辑）
func (r *EmailAccountRepositoryImpl) FindByAccountID(ctx context.Context, accountID int64) (*entity.EmailAccount, error) {
	var model model.EmailAccountModel

	if err := r.db.WithContext(ctx).Where("account_id = ? AND deleted_at IS NULL", accountID).First(&model).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, entity.ErrAccountNotFound
		}
		return nil, fmt.Errorf("failed to find email account by account_id: %w", err)
	}

	account := r.toEntity(&model)

	// 检查系统资源权限
	if err := r.permissionChecker.CheckSystemResourcePermissionFromContext(ctx, account.IsSystem); err != nil {
		return nil, fmt.Errorf("权限检查失败: %w", err)
	}

	return account, nil
}

// FindByTenantID 根据租户ID查找邮件账号列表
func (r *EmailAccountRepositoryImpl) FindByTenantID(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.EmailAccount, int64, error) {
	var models []model.EmailAccountModel
	var total int64

	// 构建基础查询 - 使用int64类型的tenantID
	db := r.db.WithContext(ctx).Where("tenant_id = ? AND deleted_at IS NULL", tenantID)

	// 获取总数
	if err := db.Model(&model.EmailAccountModel{}).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count email accounts: %w", err)
	}

	// 获取分页数据
	if err := db.Offset(offset).Limit(limit).Find(&models).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to find email accounts: %w", err)
	}

	// 转换为实体
	accounts := make([]*entity.EmailAccount, len(models))
	for i, model := range models {
		accounts[i] = r.toEntity(&model)
	}

	return accounts, total, nil
}

// FindActiveByTenantID 根据租户ID查找激活的邮件账号（自动权限过滤）
func (r *EmailAccountRepositoryImpl) FindActiveByTenantID(ctx context.Context, tenantID int64) ([]*entity.EmailAccount, error) {
	var models []model.EmailAccountModel

	// 构建基础查询 - 使用int64类型的tenantID
	db := r.db.WithContext(ctx).Where("tenant_id = ? AND is_active = ? AND deleted_at IS NULL", tenantID, true)

	// 添加权限过滤条件
	filterCondition, filterArgs, err := r.permissionChecker.BuildSystemResourceFilterFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("构建权限过滤条件失败: %w", err)
	}
	if filterCondition != "" {
		db = db.Where(filterCondition, filterArgs...)
	}

	if err := db.Order("created_at ASC").Find(&models).Error; err != nil {
		return nil, fmt.Errorf("failed to find active email accounts: %w", err)
	}

	accounts := make([]*entity.EmailAccount, len(models))
	for i, model := range models {
		accounts[i] = r.toEntity(&model)
	}

	return accounts, nil
}

// FindByName 根据名称查找邮件账号（自动权限检查）
func (r *EmailAccountRepositoryImpl) FindByName(ctx context.Context, tenantID int64, name string) (*entity.EmailAccount, error) {
	var model model.EmailAccountModel
	// 构建基础查询 - 使用int64类型的tenantID
	db := r.db.WithContext(ctx).Where("tenant_id = ? AND name = ? AND deleted_at IS NULL", tenantID, name)
	if err := db.First(&model).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, entity.ErrAccountNotFound
		}
		return nil, fmt.Errorf("failed to find email account by name: %w", err)
	}
	return r.toEntity(&model), nil
}

// Update 更新邮件账号（自动权限检查）
func (r *EmailAccountRepositoryImpl) Update(ctx context.Context, account *entity.EmailAccount) error {
	// 检查系统资源权限
	if err := r.permissionChecker.CheckSystemResourcePermissionFromContext(ctx, account.IsSystem); err != nil {
		return fmt.Errorf("权限检查失败: %w", err)
	}

	model := r.toModel(account)
	if model.ID > 0 {
		r.db.WithContext(ctx).Updates(model).Where("id = ?", account.ID)
		return nil
	}
	if err := r.db.WithContext(ctx).Save(model).Error; err != nil {
		return fmt.Errorf("failed to update email account: %w", err)
	}

	return nil
}

// UpdateSentCount 更新发送计数
func (r *EmailAccountRepositoryImpl) UpdateSentCount(ctx context.Context, id int64, sentToday, sentThisMonth int) error {
	updates := map[string]interface{}{
		"sent_today":      sentToday,
		"sent_this_month": sentThisMonth,
		"last_sent_at":    gorm.Expr("NOW()"),
		"updated_at":      gorm.Expr("NOW()"),
	}

	if err := r.db.WithContext(ctx).Model(&model.EmailAccountModel{}).
		Where("account_id = ?", id).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update sent count: %w", err)
	}

	return nil
}

// UpdateTestResult 更新测试结果
func (r *EmailAccountRepositoryImpl) UpdateTestResult(ctx context.Context, id int64, testStatus entity.TestStatus, testMessage string) error {
	updates := map[string]interface{}{
		"test_status":  int(testStatus),
		"test_message": testMessage,
		"updated_at":   gorm.Expr("NOW()"),
	}

	if err := r.db.WithContext(ctx).Model(&model.EmailAccountModel{}).
		Where("account_id = ?", id).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update test result: %w", err)
	}

	return nil
}

// Delete 软删除邮件账号（自动权限检查）
func (r *EmailAccountRepositoryImpl) Delete(ctx context.Context, id int64) error {
	// 先查找账号以检查是否为系统资源
	account, err := r.FindByID(ctx, id)
	if err != nil {
		return err
	}

	// 检查系统资源权限（FindByID已经检查过了，但为了明确性再检查一次）
	if err := r.permissionChecker.CheckSystemResourcePermissionFromContext(ctx, account.IsSystem); err != nil {
		return fmt.Errorf("权限检查失败: %w", err)
	}

	// 执行软删除
	if err := r.db.WithContext(ctx).Where("account_id = ?", id).Update("deleted_at", time.Now()).Error; err != nil {
		return fmt.Errorf("failed to soft delete email account: %w", err)
	}

	return nil
}

// ResetDailyCount 重置日发送计数
func (r *EmailAccountRepositoryImpl) ResetDailyCount(ctx context.Context, tenantID int64) error {
	updates := map[string]interface{}{
		"sent_today": 0,
		"updated_at": gorm.Expr("NOW()"),
	}

	if err := r.db.WithContext(ctx).Model(&model.EmailAccountModel{}).
		Where("tenant_id = ?", tenantID).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to reset daily count: %w", err)
	}

	return nil
}

// ResetMonthlyCount 重置月发送计数
func (r *EmailAccountRepositoryImpl) ResetMonthlyCount(ctx context.Context, tenantID int64) error {
	updates := map[string]interface{}{
		"sent_this_month": 0,
		"updated_at":      gorm.Expr("NOW()"),
	}

	if err := r.db.WithContext(ctx).Model(&model.EmailAccountModel{}).
		Where("tenant_id = ?", tenantID).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to reset monthly count: %w", err)
	}

	return nil
}

// List 获取邮件账号列表
func (r *EmailAccountRepositoryImpl) List(ctx context.Context, tenantID int64, offset, limit int) ([]*entity.EmailAccount, int64, error) {
	return r.FindByTenantID(ctx, tenantID, offset, limit)
}

// toModel 将实体转换为模型
func (r *EmailAccountRepositoryImpl) toModel(account *entity.EmailAccount) *model.EmailAccountModel {
	return &model.EmailAccountModel{
		AccountID:      account.ID,            // 使用分布式ID
		TenantID:       account.TenantID,      // 直接使用int64类型
		InternalAppID:  account.InternalAppID, // 应用ID，bigint类型提升性能
		Name:           account.Name,
		Type:           int(account.Type),
		Provider:       account.Provider,
		Host:           account.Host,
		Port:           account.Port,
		Username:       account.Username,
		Password:       account.Password,
		FromAddress:    account.FromAddress,
		FromName:       account.FromName,
		ReplyToAddress: account.ReplyToAddress,
		IsSSL:          account.IsSSL,
		IsActive:       account.IsActive,
		DailyLimit:     account.DailyLimit,
		MonthlyLimit:   account.MonthlyLimit,
		SentToday:      account.SentToday,
		SentThisMonth:  account.SentThisMonth,
		LastSentAt:     account.LastSentAt,
		TestStatus:     int(account.TestStatus),
		TestMessage:    account.TestMessage,
		Config:         model.JSONMap(account.Config.ToMap()),
		CreatedAt:      account.CreatedAt,
		UpdatedAt:      account.UpdatedAt,
		DeletedAt:      gorm.DeletedAt{},
		CreatedBy:      account.CreatedBy,
		UpdatedBy:      account.UpdatedBy,
		Version:        account.Version,
		IsSystem:       account.IsSystem,
	}
}

// toEntity 将模型转换为实体
func (r *EmailAccountRepositoryImpl) toEntity(model *model.EmailAccountModel) *entity.EmailAccount {
	return &entity.EmailAccount{
		ID:             model.AccountID,     // 使用分布式ID
		TenantID:       model.TenantID,      // 直接使用int64类型
		InternalAppID:  model.InternalAppID, // 应用ID，bigint类型提升性能
		Name:           model.Name,
		Type:           entity.AccountType(model.Type),
		Provider:       model.Provider,
		Host:           model.Host,
		Port:           model.Port,
		Username:       model.Username,
		Password:       model.Password,
		FromAddress:    model.FromAddress,
		FromName:       model.FromName,
		ReplyToAddress: model.ReplyToAddress,
		IsSSL:          model.IsSSL,
		IsActive:       model.IsActive,
		DailyLimit:     model.DailyLimit,
		MonthlyLimit:   model.MonthlyLimit,
		SentToday:      model.SentToday,
		SentThisMonth:  model.SentThisMonth,
		LastSentAt:     model.LastSentAt,
		TestStatus:     entity.TestStatus(model.TestStatus),
		TestMessage:    model.TestMessage,
		Config: func() entity.EmailAccountConfig {
			cfg := entity.EmailAccountConfig{}
			cfg.UpdateFromMap(map[string]interface{}(model.Config))
			return cfg
		}(),
		CreatedAt: model.CreatedAt,
		UpdatedAt: model.UpdatedAt,
		DeletedAt: nil,
		CreatedBy: model.CreatedBy,
		UpdatedBy: model.UpdatedBy,
		Version:   model.Version,
		IsSystem:  model.IsSystem,
	}
}
