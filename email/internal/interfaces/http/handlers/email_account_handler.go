package handlers

import (
	"net/http"
	"strconv"

	"gitee.com/heiyee/platforms/email/internal/application/email/dto"
	"gitee.com/heiyee/platforms/email/internal/application/email/service"
	"gitee.com/heiyee/platforms/email/internal/domain/email/entity"
	commonResponse "gitee.com/heiyee/platforms/pkg/common/response"
	"gitee.com/heiyee/platforms/pkg/logiface"
	"gitee.com/heiyee/platforms/pkg/usercontext"

	"github.com/gin-gonic/gin"
)

// EmailAccountHandler 邮件账号处理器
type EmailAccountHandler struct {
	accountService *service.EmailAccountApplicationService
	logger         logiface.Logger
}

// NewEmailAccountHandler 构造函数
func NewEmailAccountHandler(accountService *service.EmailAccountApplicationService, logger logiface.Logger) *EmailAccountHandler {
	return &EmailAccountHandler{accountService: accountService, logger: logger}
}

// EmailProviderResponse 用于HTTP返回的服务商DTO（snake_case）
type EmailProviderResponse struct {
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
	IconURL     string `json:"icon_url,omitempty"`
	SMTPHost    string `json:"smtp_host,omitempty"`
	SMTPPort    int    `json:"smtp_port,omitempty"`
	IMAPHost    string `json:"imap_host,omitempty"`
	IMAPPort    int    `json:"imap_port,omitempty"`
	POP3Host    string `json:"pop3_host,omitempty"`
	POP3Port    int    `json:"pop3_port,omitempty"`
	UseSSL      bool   `json:"use_ssl"`
}

// toEmailProviderResponse 转换模型为DTO
func toEmailProviderResponse(m interface{}) *EmailProviderResponse {
	type provider struct {
		Name        string
		DisplayName string
		IconURL     string
		SMTPHost    string
		SMTPPort    int
		IMAPHost    string
		IMAPPort    int
		POP3Host    string
		POP3Port    int
		UseSSL      bool
	}
	p := m.(provider) // we rely on identical field names; used only within this package
	return &EmailProviderResponse{
		Name:        p.Name,
		DisplayName: p.DisplayName,
		IconURL:     p.IconURL,
		SMTPHost:    p.SMTPHost,
		SMTPPort:    p.SMTPPort,
		IMAPHost:    p.IMAPHost,
		IMAPPort:    p.IMAPPort,
		POP3Host:    p.POP3Host,
		POP3Port:    p.POP3Port,
		UseSSL:      p.UseSSL,
	}
}

// ListEmailProviders 获取邮箱服务商列表（包含图标与预设）
func (h *EmailAccountHandler) ListEmailProviders(c *gin.Context) {
	providers, err := h.accountService.ListProviders(c.Request.Context())
	if err != nil {
		commonResponse.InternalError(c, err)
		return
	}
	// 转换为snake_case DTO
	resp := make([]*EmailProviderResponse, 0, len(providers))
	for _, p := range providers {
		// inline conversion to avoid import cycle; map fields manually
		resp = append(resp, &EmailProviderResponse{
			Name:        p.Name,
			DisplayName: p.DisplayName,
			IconURL:     p.IconURL,
			SMTPHost:    p.SMTPHost,
			SMTPPort:    p.SMTPPort,
			IMAPHost:    p.IMAPHost,
			IMAPPort:    p.IMAPPort,
			POP3Host:    p.POP3Host,
			POP3Port:    p.POP3Port,
			UseSSL:      p.UseSSL,
		})
	}
	commonResponse.Success(c, resp)
}

// GetEmailProviderByName 按名称获取服务商
func (h *EmailAccountHandler) GetEmailProviderByName(c *gin.Context) {
	name := c.Query("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{"code": 1001, "message": "name is required"})
		return
	}
	p, err := h.accountService.GetProviderByName(c.Request.Context(), name)
	if err != nil {
		commonResponse.InternalError(c, err)
		return
	}
	res := &EmailProviderResponse{
		Name:        p.Name,
		DisplayName: p.DisplayName,
		IconURL:     p.IconURL,
		SMTPHost:    p.SMTPHost,
		SMTPPort:    p.SMTPPort,
		IMAPHost:    p.IMAPHost,
		IMAPPort:    p.IMAPPort,
		POP3Host:    p.POP3Host,
		POP3Port:    p.POP3Port,
		UseSSL:      p.UseSSL,
	}
	commonResponse.Success(c, res)
}

// CreateEmailAccount 创建邮件账号
func (h *EmailAccountHandler) CreateEmailAccount(c *gin.Context) {
	var request dto.CreateEmailAccountRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantId, _ := usercontext.GetTenantID(c.Request.Context())

	account, err := h.accountService.CreateEmailAccount(c.Request.Context(), tenantId, &request)
	if err != nil {
		// 使用统一错误码处理
		switch err {
		case entity.ErrAccountAlreadyExists:
			commonResponse.UniqueConstraintError(c, "name", request.Name)
		case entity.ErrInvalidAccountName:
			commonResponse.FieldError(c, "name", "邮件账号名称无效")
		case entity.ErrInvalidProvider:
			commonResponse.FieldError(c, "provider", "服务提供商无效")
		case entity.ErrInvalidFromAddress:
			commonResponse.FieldError(c, "from_address", "发信地址无效")
		case entity.ErrInvalidSMTPHost:
			commonResponse.FieldError(c, "host", "SMTP服务器地址无效")
		case entity.ErrInvalidSMTPPort:
			commonResponse.FieldError(c, "port", "SMTP端口无效")
		case entity.ErrInvalidSMTPUsername:
			commonResponse.FieldError(c, "username", "SMTP用户名无效")
		case entity.ErrInvalidSMTPPassword:
			commonResponse.FieldError(c, "password", "SMTP密码无效")
		default:
			HandleEmailError(c, err)
		}
		return
	}

	commonResponse.Created(c, account)
}

// UpdateEmailAccount 更新邮件账号
func (h *EmailAccountHandler) UpdateEmailAccount(c *gin.Context) {
	var request dto.UpdateEmailAccountRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantId, _ := usercontext.GetTenantID(c.Request.Context())

	account, err := h.accountService.UpdateEmailAccount(c.Request.Context(), tenantId, &request)
	if err != nil {
		switch err {
		case entity.ErrAccountAlreadyExists:
			commonResponse.UniqueConstraintError(c, "name", request.Name)
		case entity.ErrInvalidAccountName:
			commonResponse.FieldError(c, "name", "邮件账号名称无效")
		case entity.ErrInvalidProvider:
			commonResponse.FieldError(c, "provider", "服务提供商无效")
		case entity.ErrInvalidFromAddress:
			commonResponse.FieldError(c, "from_address", "发信地址无效")
		case entity.ErrInvalidSMTPHost:
			commonResponse.FieldError(c, "host", "SMTP服务器地址无效")
		case entity.ErrInvalidSMTPPort:
			commonResponse.FieldError(c, "port", "SMTP端口无效")
		case entity.ErrInvalidSMTPUsername:
			commonResponse.FieldError(c, "username", "SMTP用户名无效")
		case entity.ErrInvalidSMTPPassword:
			commonResponse.FieldError(c, "password", "SMTP密码无效")
		default:
			HandleEmailError(c, err)
		}
		return
	}

	commonResponse.Updated(c, account)
}

// GetEmailAccount 获取邮件账号
func (h *EmailAccountHandler) GetEmailAccount(c *gin.Context) {
	accountIDStr := c.Query("account_id")
	if accountIDStr == "" {
		commonResponse.FieldError(c, "account_id", "账号ID不能为空")
		return
	}

	// 将accountID转换为int64
	accountID, err := strconv.ParseInt(accountIDStr, 10, 64)
	if err != nil {
		commonResponse.FieldError(c, "account_id", "账号ID格式错误")
		return
	}

	request := &dto.GetEmailAccountRequest{
		AccountID: accountID,
	}

	account, err := h.accountService.GetEmailAccount(c.Request.Context(), request)
	if err != nil {
		HandleEmailError(c, err)
		return
	}

	commonResponse.Success(c, account)
}

// GetEmailAccountList 获取邮件账号列表
func (h *EmailAccountHandler) GetEmailAccountList(c *gin.Context) {
	var request dto.ListEmailAccountsRequest
	if err := c.ShouldBindQuery(&request); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantId, _ := usercontext.GetTenantID(c.Request.Context())

	// 验证分页参数
	if request.Page <= 0 {
		request.Page = 1
	}
	if request.PageSize <= 0 || request.PageSize > 100 {
		request.PageSize = 20
	}

	result, err := h.accountService.ListEmailAccounts(c.Request.Context(), tenantId, &request)
	if err != nil {
		HandleEmailError(c, err)
		return
	}

	commonResponse.Paginated(c, result.Accounts, result.Page, result.PageSize, result.Total)
}

// TestEmailAccount 测试邮件账号
func (h *EmailAccountHandler) TestEmailAccount(c *gin.Context) {
	var request dto.TestEmailAccountRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantId, _ := usercontext.GetTenantID(c.Request.Context())

	result, err := h.accountService.TestEmailAccount(c.Request.Context(), tenantId, &request)
	if err != nil {
		switch err {
		case entity.ErrAccountInactive:
			commonResponse.BusinessError(c, commonResponse.CodeBusinessLogicError, "邮件账号未激活")
		default:
			HandleEmailError(c, err)
		}
		return
	}

	commonResponse.Success(c, result)
}

// DeleteEmailAccount 删除邮件账号
func (h *EmailAccountHandler) DeleteEmailAccount(c *gin.Context) {
	var request dto.DeleteEmailAccountRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		commonResponse.GinValidationError(c, err)
		return
	}

	// 从usercontext获取租户ID
	tenantId, _ := usercontext.GetTenantID(c.Request.Context())

	err := h.accountService.DeleteEmailAccount(c.Request.Context(), tenantId, &request)
	if err != nil {
		HandleEmailError(c, err)
		return
	}

	commonResponse.Deleted(c)
}

// GetEmailAccountTypes 获取邮件账号类型列表
func (h *EmailAccountHandler) GetEmailAccountTypes(c *gin.Context) {
	items, err := h.accountService.GetAccountTypes(c.Request.Context())
	if err != nil {
		commonResponse.InternalError(c, err)
		return
	}
	commonResponse.Success(c, items)
}
