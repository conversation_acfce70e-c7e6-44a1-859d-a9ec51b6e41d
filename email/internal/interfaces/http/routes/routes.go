package routes

import (
	"context"
	"time"

	"gitee.com/heiyee/platforms/email/internal/infrastructure/container"
	"gitee.com/heiyee/platforms/pkg/logiface"

	"gitee.com/heiyee/platforms/email/internal/infrastructure/external"
	"gitee.com/heiyee/platforms/email/internal/interfaces/http/handlers"
	"gitee.com/heiyee/platforms/pkg/httpmiddleware"

	"github.com/gin-gonic/gin"
)

// 全局URL前缀常量
const (
	APIPrefix    = "/api/email"     // 邮件系统API前缀
	TenantPrefix = "/api/v1/tenant" // 租户管理API前缀
)

// UserInfoProviderAdapter 用户信息提供者适配器
type UserInfoProviderAdapter struct {
	UserClient *external.UserServiceClient
}

// GetUserInfo 实现 UserInfoProvider 接口
func (a *UserInfoProviderAdapter) GetUserInfo(ctx context.Context, token string) *httpmiddleware.AuthedUser {
	logger := logiface.GetLogger()
	userClient := a.UserClient
	resp, err := userClient.GetUserInfoByToken(ctx, token)
	if err != nil {
		logger.Warn(ctx, "Failed to get user info by token",
			logiface.Error(err),
			logiface.String("token_prefix", token[:min(len(token), 10)]+"..."))
		return nil
	}

	if resp == nil || resp.Code != 0 || resp.Data == nil {
		logger.Warn(ctx, "Invalid or expired token",
			logiface.Int("response_code", int(resp.Code)),
			logiface.String("response_message", resp.Message))
		return nil
	}

	return &httpmiddleware.AuthedUser{
		UserId:   resp.Data.UserId,
		Username: resp.Data.Username,
		RealName: resp.Data.RealName,
		Email:    resp.Data.Email,
		TenantId: resp.Data.TenantId,
	}
}

// AppInfoProviderAdapter 租户信息提供者适配器
type AppInfoProviderAdapter struct {
	UserClient *external.UserServiceClient
}

// GetAppInfo 实现 AppInfoProvider 接口
func (a *AppInfoProviderAdapter) GetAppInfo(ctx context.Context, appId string) *httpmiddleware.AppInfo {
	logger := logiface.GetLogger()
	userClient := a.UserClient

	// 使用新的 GetAppInfo 方法
	resp, err := userClient.GetAppInfo(ctx, 0, appId) // internalAppID 设为 0，使用 appId
	if err != nil {
		logger.Warn(ctx, "Failed to get app info by appId",
			logiface.Error(err),
			logiface.String("app_id", appId))
		return nil
	}

	if resp == nil || resp.Code != 0 || resp.Data == nil {
		logger.Warn(ctx, "Invalid app info",
			logiface.Int("response_code", int(resp.Code)),
			logiface.String("response_message", resp.Message),
			logiface.String("app_id", appId))
		return nil
	}

	return &httpmiddleware.AppInfo{
		TenantId:      resp.Data.TenantId,
		AppId:         resp.Data.AppId,
		InternalAppId: resp.Data.InternalAppId,
	}
}

// min 辅助函数，返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// RouterConfig 路由配置 - 统一邮件系统路由管理
type RouterConfig struct {
	TemplateHandler     *handlers.TemplateHandler
	EmailHandler        *handlers.EmailHandler
	EmailAccountHandler *handlers.EmailAccountHandler
	TenantHandler       *handlers.TenantHandler
	ServiceName         string
	AppLogger           logiface.Logger
	Container           *container.DependencyContainer
}

// SetupRoutes 设置所有邮件系统路由 (前后端分离架构)
func SetupRoutes(r *gin.Engine, config *RouterConfig) {
	auth := r.Group("")
	httpmiddleware.SetupCommonMiddleware(auth, &httpmiddleware.MiddlewareConfig{
		ServiceName:           config.ServiceName,
		EnableRequestID:       true,
		EnableSecurityHeaders: true,
		EnableRecovery:        true,
		EnableMetrics:         true,
		EnableRequestSize:     true,
		MaxRequestSize:        10 << 20, // 10MB
		EnableAPIVersion:      false,
		EnableTimeout:         true,
		RequestTimeout:        10 * time.Second,
		EnableTraceID:         true,
		Logger:                config.AppLogger,
		EnableAccessLog:       true,
		EnableUserInfo:        true,
		UserInfoProvider:      &UserInfoProviderAdapter{config.Container.Infrastructure.UserClient},
		AppInfoProvider:       &AppInfoProviderAdapter{config.Container.Infrastructure.UserClient},
	})
	// 全局校验登录
	auth.Use(httpmiddleware.RequireAuthedMiddleware())
	// 邮件系统API路由组
	mailAPI := auth.Group(APIPrefix)
	{
		// 邮件模板管理
		if config.TemplateHandler != nil {
			setupTemplateRoutes(mailAPI, config.TemplateHandler)
		}

		// 邮件账号管理
		if config.EmailAccountHandler != nil {
			setupEmailAccountRoutes(mailAPI, config.EmailAccountHandler)
		}

		// 邮件发送管理
		if config.EmailHandler != nil {
			setupEmailRoutes(mailAPI, config.EmailHandler)
		}
	}

	// 租户管理API路由组
	if config.TenantHandler != nil {
		setupTenantRoutes(auth, config.TenantHandler)
	}
}

// setupTemplateRoutes 设置邮件模板路由
// 路径: /api/email/templates/*
func setupTemplateRoutes(rg *gin.RouterGroup, handler *handlers.TemplateHandler) {
	templates := rg.Group("/templates")
	{
		// 需要认证的接口（全局已有AuthUserMiddleware，无需重复添加）
		authorized := templates.Group("")
		{
			// 新的重构接口
			authorized.POST("/save", handler.SaveOrUpdateTemplate)         // 保存/更新基本信息
			authorized.POST("/content", handler.UpdateTemplateContent)     // 更新模板内容
			authorized.POST("/variables", handler.UpdateTemplateVariables) // 更新模板变量

			// 保留的接口
			authorized.POST("list", handler.ListTemplates)                  // 获取模板列表
			authorized.GET("/get", handler.GetTemplate)                     // 获取模板详情
			authorized.POST("/delete", handler.DeleteTemplate)              // 删除模板
			authorized.POST("/variables/get", handler.GetTemplateVariables) // 获取模板变量列表

		}
	}
}

// setupEmailAccountRoutes 设置邮件账号管理路由
// 路径: /api/email/accounts/*
func setupEmailAccountRoutes(rg *gin.RouterGroup, handler *handlers.EmailAccountHandler) {
	accounts := rg.Group("/accounts")
	{
		// 创建邮件账号
		accounts.POST("/create", handler.CreateEmailAccount)

		// 更新邮件账号
		accounts.POST("/update", handler.UpdateEmailAccount)

		// 获取邮件账号详情
		accounts.GET("/detail", handler.GetEmailAccount)

		// 获取邮件账号列表
		accounts.POST("/list", handler.GetEmailAccountList)

		// 测试邮件账号
		accounts.POST("/test", handler.TestEmailAccount)

		// 删除邮件账号
		accounts.POST("/delete", handler.DeleteEmailAccount)

		// 获取邮件账号类型列表
		accounts.GET("/types", handler.GetEmailAccountTypes)

		// 获取邮箱服务商列表（含图标与预设配置）
		accounts.GET("/providers", handler.ListEmailProviders)
		// 获取单个服务商详情（按名称）
		accounts.GET("/provider/detail", handler.GetEmailProviderByName)
	}
}

// setupEmailRoutes 设置邮件发送管理路由
// 路径: /api/email/emails/*
func setupEmailRoutes(rg *gin.RouterGroup, handler *handlers.EmailHandler) {
	emails := rg.Group("/emails")
	{
		// 基于模板发送邮件
		emails.POST("/send/template", handler.SendTemplateEmail)

		// 发送邮件
		emails.POST("/send", handler.SendEmail)

		// 获取邮件状态 (修改：移除path参数，使用查询参数)
		emails.GET("/detail", handler.GetEmailStatus)

		// 获取邮件统计
		emails.GET("/statistics", handler.GetEmailStatistics)
	}
}

// setupTenantRoutes 设置租户管理路由
// 路径: /api/v1/tenant/*
func setupTenantRoutes(r *gin.RouterGroup, handler *handlers.TenantHandler) {
	tenantGroup := r.Group(TenantPrefix)
	// 需要认证的路由
	tenantGroup.Use(httpmiddleware.RequireAuthedMiddleware())
	{
		// 获取租户配置
		tenantGroup.POST("/get-config", handler.GetConfig)

		// 更新租户配置
		tenantGroup.POST("/update-config", handler.UpdateConfig)

		// 验证SMTP配置
		tenantGroup.POST("/verify-smtp", handler.VerifySMTP)

		// 获取使用量统计
		tenantGroup.GET("/usage", handler.GetUsage)
	}
}
