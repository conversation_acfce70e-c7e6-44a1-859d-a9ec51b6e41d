package entity

import (
	"crypto/rand"
	"encoding/hex"
	"time"
)

// AccountType 邮件账号类型
type AccountType int

const (
	AccountTypeSMTP AccountType = iota + 1
	AccountTypeIMAP
	AccountTypePOP3
	AccountTypeExchange
	AccountTypeAPI
)

// TestStatus 测试状态
type TestStatus int

const (
	TestStatusUntested TestStatus = iota
	TestStatusSuccess
	TestStatusFailed
)

// EmailAccount 邮件账号实体
type EmailAccount struct {
	ID             int64 // 分布式ID，业务主键
	TenantID       int64 // 租户ID - 修改为int64类型
	InternalAppID  int64 // 应用ID，bigint类型提升性能
	Name           string
	Type           AccountType
	Provider       string
	Host           string
	Port           int
	Username       string
	Password       string
	FromAddress    string
	FromName       string
	ReplyToAddress string
	IsSSL          bool
	IsActive       bool
	DailyLimit     int
	MonthlyLimit   int
	SentToday      int
	SentThisMonth  int
	LastSentAt     *time.Time
	TestStatus     TestStatus
	TestMessage    string
	// Config 使用结构体承载，保持可维护性
	Config    EmailAccountConfig
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt *time.Time
	CreatedBy string
	UpdatedBy string
	Version   int
	IsSystem  bool
}

// SetSMTPConfig 设置SMTP配置
func (a *EmailAccount) SetSMTPConfig(host string, port int, username, password string) {
	a.Host = host
	a.Port = port
	a.Username = username
	a.Password = password
	a.UpdatedAt = time.Now()
}

// SetLimits 设置发送限制
func (a *EmailAccount) SetLimits(dailyLimit, monthlyLimit int) {
	a.DailyLimit = dailyLimit
	a.MonthlyLimit = monthlyLimit
	a.UpdatedAt = time.Now()
}

// SetTestResult 设置测试结果
func (a *EmailAccount) SetTestResult(status TestStatus, message string) {
	a.TestStatus = status
	a.TestMessage = message
	a.UpdatedAt = time.Now()
}

// IncrementSentCount 增加发送计数
func (a *EmailAccount) IncrementSentCount() error {
	if !a.IsActive {
		return NewBusinessError("邮件账号未激活")
	}

	now := time.Now()
	today := now.Format("2006-01-02")

	// 检查是否是新的一天
	if a.LastSentAt == nil || a.LastSentAt.Format("2006-01-02") != today {
		a.SentToday = 0
	}

	// 检查是否是新的月份
	thisMonth := now.Format("2006-01")
	if a.LastSentAt == nil || a.LastSentAt.Format("2006-01") != thisMonth {
		a.SentThisMonth = 0
	}

	// 检查日限制
	if a.DailyLimit > 0 && a.SentToday >= a.DailyLimit {
		return NewBusinessError("已达到日发送限制")
	}

	// 检查月限制
	if a.MonthlyLimit > 0 && a.SentThisMonth >= a.MonthlyLimit {
		return NewBusinessError("已达到月发送限制")
	}

	a.SentToday++
	a.SentThisMonth++
	a.LastSentAt = &now
	a.UpdatedAt = now

	return nil
}

// CanSend 检查是否可以发送
func (a *EmailAccount) CanSend() error {
	if !a.IsActive {
		return NewBusinessError("邮件账号未激活")
	}

	if a.TestStatus == TestStatusFailed {
		return NewBusinessError("邮件账号测试失败")
	}

	now := time.Now()
	today := now.Format("2006-01-02")

	// 检查日限制
	if a.DailyLimit > 0 && a.LastSentAt != nil && a.LastSentAt.Format("2006-01-02") == today && a.SentToday >= a.DailyLimit {
		return NewBusinessError("已达到日发送限制")
	}

	// 检查月限制
	thisMonth := now.Format("2006-01")
	if a.MonthlyLimit > 0 && a.LastSentAt != nil && a.LastSentAt.Format("2006-01") == thisMonth && a.SentThisMonth >= a.MonthlyLimit {
		return NewBusinessError("已达到月发送限制")
	}

	return nil
}

// Activate 激活账号
func (a *EmailAccount) Activate() {
	a.IsActive = true
	a.UpdatedAt = time.Now()
}

// Deactivate 停用账号
func (a *EmailAccount) Deactivate() {
	a.IsActive = false
	a.UpdatedAt = time.Now()
}

// UpdateConfig 保持兼容旧签名：将 map 写入到结构体配置
func (a *EmailAccount) UpdateConfig(config map[string]interface{}) {
	a.Config.UpdateFromMap(config)
	a.UpdatedAt = time.Now()
}

// Validate 验证邮件账号
func (a *EmailAccount) Validate() error {
	if a.TenantID == 0 {
		return NewValidationError("tenant_id", "租户ID不能为空")
	}
	if a.Name == "" {
		return NewValidationError("name", "账号名称不能为空")
	}
	if a.FromAddress == "" {
		return NewValidationError("from_address", "发送地址不能为空")
	}
	if a.Type == AccountTypeSMTP {
		if a.Host == "" {
			return NewValidationError("host", "SMTP主机不能为空")
		}
		if a.Port <= 0 || a.Port > 65535 {
			return NewValidationError("port", "端口号必须在1-65535之间")
		}
	}

	return nil
}

// generateAccountID 生成账号ID
// 注意：此函数已被废弃，请使用 EntityFactory 和 IDGenerator
// func generateAccountID() (int64, error) {
// 	// 使用雪花算法生成账号ID
// 	accountID := id.GenerateID()
// 	return accountID, nil
// }

// generateRandomString 生成随机字符串
func generateRandomString(n int) (string, error) {
	bytes := make([]byte, n)
	_, err := rand.Read(bytes)
	if err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}
