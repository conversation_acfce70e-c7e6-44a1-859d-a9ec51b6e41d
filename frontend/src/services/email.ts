import {API_ENDPOINTS, apiService} from '../utils/request';
import type {ApiResponse, EmailAccount} from '../types';
import {
  CreateEmailAccountRequest,
  EmailTemplate,
  ListEmailAccountsRequest,
  ListEmailAccountsResponse,
  TestEmailAccountRequest,
  UpdateEmailAccountRequest,
  TemplateSelectRequest,
  TemplateSelectResponse,
  UpdateTemplateContentRequest,
  UpdateTemplateVariablesRequest,
  TemplateVariable,
  SaveOrUpdateTemplateRequest,
  SaveOrUpdateTemplateResponse,
} from '../types';

// Email Account API Service
class EmailAccountService {
  // 获取邮件账号列表
  async getEmailAccounts(params?: ListEmailAccountsRequest): Promise<ApiResponse<EmailAccount[]>> {
    // API直接返回EmailAccount数组，而不是ListEmailAccountsResponse格式
    return await apiService.post(API_ENDPOINTS.EMAIL.ACCOUNTS_LIST, params);
  }

  // 创建邮件账号
  async createEmailAccount(data: CreateEmailAccountRequest): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.post(API_ENDPOINTS.EMAIL.ACCOUNTS_CREATE, data);
  }

  // 更新邮件账号
  async updateEmailAccount(data: UpdateEmailAccountRequest): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.post(API_ENDPOINTS.EMAIL.ACCOUNTS_UPDATE, data);
  }

  // 获取邮件账号详情
  async getEmailAccount(id: string): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.get(API_ENDPOINTS.EMAIL.ACCOUNTS_DETAIL, { params: { account_id: id } });
  }

  // 删除邮件账号
  async deleteEmailAccount(id: number): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.post(API_ENDPOINTS.EMAIL.ACCOUNTS_DELETE, {id});
  }

  // 测试邮件账号
  async testEmailAccount(data: TestEmailAccountRequest): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.post(API_ENDPOINTS.EMAIL.ACCOUNTS_TEST, data);
  }

  // 获取邮件账号类型列表
  async getEmailAccountTypes(): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.get(API_ENDPOINTS.EMAIL.ACCOUNTS_TYPES);
  }

  // 获取邮件账号测试状态列表
  async getEmailAccountTestStatus(): Promise<ApiResponse<any>> {
    // 返回完整的响应结构，让调用方处理业务状态码
    return await apiService.get(API_ENDPOINTS.EMAIL.ACCOUNTS_TEST_STATUS);
  }

  // 获取服务商列表（含图标与预设）
  async getProviders(): Promise<ApiResponse<any[]>> {
    return await apiService.get(API_ENDPOINTS.EMAIL.ACCOUNTS_PROVIDERS);
  }

  // 获取服务商详情（按名称）
  async getProviderByName(name: string): Promise<ApiResponse<any>> {
    return await apiService.get(API_ENDPOINTS.EMAIL.ACCOUNTS_PROVIDER_DETAIL, { params: { name } });
  }
}

export const emailAccountService = new EmailAccountService();

// Email Template API Service
class EmailTemplateService {
  // 智能模板选择
  async selectTemplate(data: TemplateSelectRequest): Promise<ApiResponse<TemplateSelectResponse>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_SELECT, data);
  }

  // 获取模板列表
  async getTemplateList(params: any): Promise<ApiResponse<EmailTemplate[]>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_LIST, params);
  }

  // 获取模板详情
  async getTemplate(id: number): Promise<ApiResponse<EmailTemplate>> {
    return await apiService.get(`${API_ENDPOINTS.EMAIL.TEMPLATES_GET}?id=${id}`);
  }

  // 保存/更新模板基本信息（新接口）
  async saveOrUpdateTemplate(data: SaveOrUpdateTemplateRequest): Promise<ApiResponse<SaveOrUpdateTemplateResponse>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_SAVE, data);
  }

  // 更新模板内容（新接口）
  async updateTemplateContent(data: UpdateTemplateContentRequest): Promise<ApiResponse<EmailTemplate>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_CONTENT, data);
  }

  // 更新模板变量（新接口）
  async updateTemplateVariables(data: UpdateTemplateVariablesRequest): Promise<ApiResponse<EmailTemplate>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_VARIABLES_UPDATE, data);
  }

  // 删除模板
  async deleteTemplate(id: number): Promise<ApiResponse<void>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_DELETE, { id });
  }

  // 获取模板变量列表
  async getTemplateVariables(templateId: number): Promise<ApiResponse<TemplateVariable[]>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_VARIABLES_GET, { template_id: templateId });
  }

  // 预览模板
  async previewTemplate(request: any): Promise<ApiResponse<any>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_PREVIEW, request);
  }

  // 克隆系统模板
  async cloneSystemTemplate(request: any): Promise<ApiResponse<any>> {
    return await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_CLONE, request);
  }

  // 获取模板代码列表（用于下拉框）
  async getTemplateCodeList(tenantId: number, appId?: string): Promise<ApiResponse<Array<{code: string, name: string}>>> {
    const params: any = {
      page: 1,
      page_size: 1000, // 获取所有模板
      tenant_id: tenantId
    };
    
    const response = await apiService.post(API_ENDPOINTS.EMAIL.TEMPLATES_LIST, params, {
      headers: appId ? { 'X-App-Id': appId } : undefined,
    });
    
    // 如果成功，转换数据格式为下拉框所需的格式
    if (response.code === 200 && response.data) {
      const templates = Array.isArray(response.data) ? response.data : response.data.templates || [];
      const codeList = templates.map((template: any) => ({
        code: template.code,
        name: `${template.name} (${template.code})`
      }));
      
      return {
        ...response,
        data: codeList
      };
    }
    
    return response;
  }
}

export const emailTemplateService = new EmailTemplateService(); 