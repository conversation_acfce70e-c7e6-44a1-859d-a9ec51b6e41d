import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  message,
  Row,
  Col,
  Typography,
  Tooltip,
  Dropdown,
  Switch,

} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  MailOutlined,
  MoreOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,

} from '@ant-design/icons';
import { useTheme } from '../../contexts/ThemeContext';
import { emailAccountService } from '../../services/email';
import {
  EmailAccount,
  EMAIL_ACCOUNT_TYPES,
  EMAIL_ACCOUNT_TEST_STATUS,
  ListEmailAccountsRequest,
} from '../../types/email';
import AccountForm from './components/AccountForm';
import TestAccountModal from './components/TestAccountModal';
import { showAPIError } from '../../utils/errorHandler';

const { Option } = Select;
const { Title, Text } = Typography;
const { confirm } = Modal;

/**
 * 邮件账户管理页面
 */
const AccountManagement: React.FC = () => {
  const { isDarkMode } = useTheme();
  const [accounts, setAccounts] = useState<EmailAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState<ListEmailAccountsRequest>({
    page: 1,
    page_size: 10,
  });

  // 模态框状态
  const [formModalVisible, setFormModalVisible] = useState(false);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [selectedAccount, setSelectedAccount] = useState<EmailAccount | null>(null);
  const [formLoading, setFormLoading] = useState(false);

  // 统计数据
  const stats = [
    {
      title: '总账户数',
      value: pagination.total,
      icon: <MailOutlined />,
      color: '#1890ff',
    },
    {
      title: '活跃账户',
      value: accounts.filter(a => a.is_active).length,
      icon: <CheckCircleOutlined />,
      color: '#52c41a',
    },
    {
      title: '测试通过',
      value: accounts.filter(a => a.test_status === EMAIL_ACCOUNT_TEST_STATUS.SUCCESS).length,
      icon: <CheckCircleOutlined />,
      color: '#722ed1',
    },
    {
      title: '未测试',
      value: accounts.filter(a => a.test_status === EMAIL_ACCOUNT_TEST_STATUS.UNTESTED).length,
      icon: <ClockCircleOutlined />,
      color: '#fa8c16',
    },
  ];

  // 初始化数据
  useEffect(() => {
    fetchAccounts();
  }, [pagination.current, pagination.pageSize, searchParams.type, searchParams.is_active]);

  // 获取账户列表
  const fetchAccounts = async () => {
    setLoading(true);
    try {
      const params: ListEmailAccountsRequest = {
        page: pagination.current,
        page_size: pagination.pageSize,
        keyword: searchParams.keyword,
        type: searchParams.type,
        is_active: searchParams.is_active,
      };
      
      const response = await emailAccountService.getEmailAccounts(params);
      setAccounts(response.data || []);
      setPagination(prev => ({
        ...prev,
        total: response.data?.length || 0,
      }));
    } catch (error) {
      showAPIError(error);
      setAccounts([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchParams(prev => ({
      ...prev,
      keyword: value,
      page: 1,
    }));
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  // 处理类型筛选
  const handleTypeFilter = (value: string) => {
    setSearchParams(prev => ({
      ...prev,
      type: value === 'all' ? undefined : Number(value),
      page: 1,
    }));
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  // 处理状态筛选
  const handleStatusFilter = (value: string) => {
    setSearchParams(prev => ({
      ...prev,
      is_active: value === 'all' ? undefined : value === 'active',
      page: 1,
    }));
    setPagination(prev => ({
      ...prev,
      current: 1,
    }));
  };

  // 处理创建账户
  const handleCreate = () => {
    setSelectedAccount(null);
    setFormModalVisible(true);
  };

  // 处理编辑账户
  const handleEdit = (account: EmailAccount) => {
    setSelectedAccount(account);
    setFormModalVisible(true);
  };

  // 处理删除账户
  const handleDelete = (account: EmailAccount) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除账户 "${account.name}" 吗？此操作不可恢复。`,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await emailAccountService.deleteEmailAccount(account.id);
          message.success('账户删除成功');
          fetchAccounts();
        } catch (error) {
          showAPIError(error);
        }
      },
    });
  };

  // 处理测试账户
  const handleTest = (account: EmailAccount) => {
    setSelectedAccount(account);
    setTestModalVisible(true);
  };

  // 处理切换账户状态
  const handleToggleStatus = async (account: EmailAccount) => {
    try {
      await emailAccountService.updateEmailAccount({
        id: account.id,
        is_active: !account.is_active,
      });
      message.success(`账户已${account.is_active ? '禁用' : '启用'}`);
      fetchAccounts();
    } catch (error) {
      showAPIError(error);
    }
  };

  // 处理表单提交
  const handleFormSubmit = async (values: any) => {
    setFormLoading(true);
    try {
      if (selectedAccount) {
        // 更新账户
        await emailAccountService.updateEmailAccount({
          ...values,
          id: selectedAccount.id,
        });
        message.success('账户更新成功');
      } else {
        // 创建账户
        const payload = {
          ...values,
          is_active: false,
        };
        await emailAccountService.createEmailAccount(payload as any);
        message.success('账户已创建成功，需要测试通过后才能启用使用');
      }
      setFormModalVisible(false);
      fetchAccounts();
    } catch (error) {
      showAPIError(error);
    } finally {
      setFormLoading(false);
    }
  };

  // 处理测试账户
  const handleTestAccount = async (data: { id: number; test_email: string }) => {
    try {
      const result = await emailAccountService.testEmailAccount(data);
      return result;
    } catch (error) {
      showAPIError(error);
      return {
        success: false,
        message: '测试失败，请检查网络连接或账户配置',
      };
    }
  };

  // 渲染账户类型标签
  const renderAccountType = (type: number) => {
    const typeMap: Record<number, { text: string; color: string }> = {
      1: { text: 'SMTP', color: 'blue' },
      2: { text: 'IMAP', color: 'purple' },
      3: { text: 'POP3', color: 'cyan' },
      4: { text: 'Exchange', color: 'green' },
      5: { text: 'API', color: 'orange' },
    };
    const config = typeMap[type] || { text: '未知', color: 'default' };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 渲染提供商标签
  const renderProvider = (provider: string) => {
    const providerColorMap: Record<string, string> = {
      'Gmail': 'red',
      'QQ': 'blue',
      'Aliyun': 'orange',
      'Exchange': 'green',
      'Custom': 'default',
    };
    return <Tag color={providerColorMap[provider] || 'default'}>{provider}</Tag>;
  };

  // 渲染状态标签
  const renderStatus = (isActive: boolean) => {
    return isActive ? (
      <Tag color="success" icon={<CheckCircleOutlined />}>启用</Tag>
    ) : (
      <Tag color="default" icon={<CloseCircleOutlined />}>禁用</Tag>
    );
  };

  // 渲染测试状态图标
  const renderTestStatus = (testStatus: number) => {
    if (testStatus === EMAIL_ACCOUNT_TEST_STATUS.SUCCESS) {
      return (
        <Tooltip title="已测试">
          <CheckCircleOutlined style={{ color: '#52c41a', fontSize: '16px' }} />
        </Tooltip>
      );
    }
    return null;
  };

  // 表格列定义
  const columns = [
    {
      title: '账户信息',
      key: 'account_info',
      render: (record: EmailAccount) => (
        <div>
          <div style={{
            fontSize: 14,
            fontWeight: 500,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {record.name}
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            {record.from_address || '未设置发件地址'}
          </div>
        </div>
      ),
    },
    {
      title: '连接配置',
      key: 'connection_config',
      render: (record: EmailAccount) => (
        <div>
          <div style={{
            fontSize: 14,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {renderAccountType(record.type)}
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            {record.provider || '自定义'}
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (record: EmailAccount) => (
        <div>
          <div style={{
            fontSize: 14,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            <Switch
              checked={record.is_active}
              onChange={() => handleToggleStatus(record)}
              size="small"
            />
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            {record.is_active ? '已启用' : '已禁用'}
          </div>
        </div>
      ),
    },
    {
      title: '发送统计',
      key: 'send_stats',
      render: (record: EmailAccount) => (
        <div>
          <div style={{
            fontSize: 14,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {record.sent_today}/{record.daily_limit}
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            今日发送/日限制
          </div>
        </div>
      ),
    },
    {
      title: '测试状态',
      key: 'test_status',
      render: (record: EmailAccount) => (
        <div>
          <div style={{
            fontSize: 14,
            color: isDarkMode ? '#ffffff' : '#262626',
            marginBottom: 4,
          }}>
            {renderTestStatus(record.test_status)}
          </div>
          <div style={{
            fontSize: 12,
            color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
          }}>
            <Button
              type="text"
              size="small"
              icon={<MailOutlined />}
              onClick={() => handleTest(record)}
            >
              测试
            </Button>
          </div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: EmailAccount) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Dropdown
            menu={{
              items: [
                {
                  key: 'toggle',
                  icon: record.is_active ? <CloseCircleOutlined /> : <CheckCircleOutlined />,
                  label: record.is_active ? '禁用' : '启用',
                  onClick: () => handleToggleStatus(record),
                },
                {
                  key: 'delete',
                  icon: <DeleteOutlined />,
                  label: '删除',
                  danger: true,
                  onClick: () => handleDelete(record),
                },
              ],
            }}
          >
            <Button
              type="text"
              size="small"
              icon={<MoreOutlined />}
            />
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <div className="account-management-page">
      {/* 统计信息区域 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          {/* 统计信息 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: 32 }}>
            {stats.map((stat, index) => (
              <div
                key={index}
                style={{ display: 'flex', alignItems: 'center', gap: 12 }}
              >
                <div
                  style={{
                    width: 40,
                    height: 40,
                    borderRadius: 8,
                    background: `${stat.color}15`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: stat.color,
                    fontSize: 18,
                  }}
                >
                  {stat.icon}
                </div>
                <div>
                  <div style={{
                    fontSize: 24,
                    fontWeight: 600,
                    color: isDarkMode ? '#ffffff' : '#262626',
                    lineHeight: 1,
                  }}>
                    {stat.value.toLocaleString()}
                  </div>
                  <div style={{
                    fontSize: 12,
                    color: isDarkMode ? '#8c8c8c' : '#8c8c8c',
                    marginTop: 2,
                  }}>
                    {stat.title}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 操作按钮 */}
          <Space>
            <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
              新增账户
            </Button>
          </Space>
        </div>
      </div>

      {/* 搜索和筛选区域 */}
      <Card
        style={{
          borderRadius: 12,
          borderStyle: 'none',
          background: isDarkMode ? '#1f1f1f' : '#ffffff',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
          marginBottom: 24,
        }}
        styles={{ body: { padding: 20 } }}
      >
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8} lg={6}>
            <Input.Search
              placeholder="搜索账户名称"
              onSearch={handleSearch}
              allowClear
            />
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Select
              placeholder="选择状态"
              onChange={handleStatusFilter}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="all">全部状态</Option>
              <Option value="active">已启用</Option>
              <Option value="inactive">已禁用</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Select
              placeholder="选择类型"
              onChange={handleTypeFilter}
              allowClear
              style={{ width: '100%' }}
            >
              <Option value="all">全部类型</Option>
              <Option value={EMAIL_ACCOUNT_TYPES.SMTP}>SMTP</Option>
              <Option value={EMAIL_ACCOUNT_TYPES.IMAP}>IMAP</Option>
              <Option value={EMAIL_ACCOUNT_TYPES.POP3}>POP3</Option>
              <Option value={EMAIL_ACCOUNT_TYPES.API}>API</Option>
            </Select>
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Space>
              <Button icon={<ReloadOutlined />} onClick={fetchAccounts}>
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 账户表格 */}
      <div style={{ marginBottom: 24 }}>
        <Card
          style={{
            borderRadius: 12,
            borderStyle: 'none',
            background: isDarkMode ? '#1f1f1f' : '#ffffff',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
            border: `1px solid ${isDarkMode ? '#303030' : '#f0f0f0'}`,
          }}
          styles={{ body: { padding: 0 } }}
        >
          <Table
            columns={columns}
            dataSource={accounts}
            rowKey="id"
            loading={loading}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              onChange: (page, pageSize) => {
                setPagination({
                  ...pagination,
                  current: page,
                  pageSize: pageSize || pagination.pageSize,
                });
              },
            }}
            style={{ background: 'transparent' }}
          />
        </Card>
      </div>

      {/* 创建/编辑账户模态框 */}
      <Modal
        title={selectedAccount ? '编辑邮件账户' : '创建邮件账户'}
        open={formModalVisible}
        onCancel={() => setFormModalVisible(false)}
        footer={null}
        width={900}
        destroyOnHidden
      >
        <AccountForm
          initialValues={selectedAccount || undefined}
          onFinish={handleFormSubmit}
          loading={formLoading}
        />
      </Modal>

      {/* 测试账户模态框 */}
      <TestAccountModal
        visible={testModalVisible}
        onCancel={() => setTestModalVisible(false)}
        onTest={handleTestAccount}
        account={selectedAccount || undefined}
      />
    </div>
  );
};

export default AccountManagement; 