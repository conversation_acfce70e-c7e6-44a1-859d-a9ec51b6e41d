import axios, {AxiosInstance, AxiosRequestConfig, AxiosResponse} from 'axios';
import {ApiResponse} from '../types/api';
import {HTTP_STATUS} from '../constants/errorCodes';

// ==================== API配置 ====================

// API配置
export const API_CONFIG = {
  // 基础配置
  BASE_URL: '', // 开发环境下必须为空，所有请求都通过代理转发
  
  // API前缀配置
  PREFIXES: {
    // 全局API前缀
    GLOBAL: '/api/user',
    
    // 各模块API前缀
    AUTH: '/api/user/auth',
    USER: '/api/user',
    ROLE: '/api/user/role',
    PERMISSION: '/api/user/permission',
    RESOURCE: '/api/user/resource',
    DEPARTMENT: '/api/user/department',
    POSITION: '/api/user/position',
    TENANT: '/api/user/tenants',
    CAPTCHA: '/api/user/captcha',
    IDGENERATOR: '/api/user/idgenerator',
    FILE_SYSTEM: '/api/user/file',
    APPLICATION: '/api/user/admin/application',
    EMAIL: '/api/email',
    VERIFICATION: '/api/user/admin/verification',
    
    // 特殊路由前缀
    ME: '/api/user/me',
    PUBLIC: '/api/user/public',
    HEALTH: '/api/user/health',
  },
  
  // 超时配置
  TIMEOUT: 10000,
  
  // 重试配置
  RETRY: {
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000,
  },
  
  // 请求头配置
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
} as const;

// API端点配置
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: `${API_CONFIG.PREFIXES.AUTH}/login`,
    MFA_LOGIN: `${API_CONFIG.PREFIXES.AUTH}/mfa-login`,
    REFRESH: `${API_CONFIG.PREFIXES.AUTH}/refresh`,
    LOGOUT: `${API_CONFIG.PREFIXES.AUTH}/logout`,
    FORGOT_PASSWORD: `${API_CONFIG.PREFIXES.AUTH}/forgot-password`,
    RESET_PASSWORD: `${API_CONFIG.PREFIXES.AUTH}/reset-password`,
    SEND_MFA_CODE: `${API_CONFIG.PREFIXES.AUTH}/send-mfa-code`,
    VERIFY_MFA_CODE: `${API_CONFIG.PREFIXES.AUTH}/verify-mfa-code`,
    SESSIONS: `${API_CONFIG.PREFIXES.AUTH}/sessions`,
    REVOKE_SESSION: `${API_CONFIG.PREFIXES.AUTH}/revoke-session`,
  },
  
  // 用户相关
  USER: {
    LIST: `${API_CONFIG.PREFIXES.USER}/list`,
    CREATE: `${API_CONFIG.PREFIXES.USER}/create`,
    GET: `${API_CONFIG.PREFIXES.USER}/get`,
    UPDATE: `${API_CONFIG.PREFIXES.USER}/update`,
    DELETE: `${API_CONFIG.PREFIXES.USER}/delete`,
    STATS: `${API_CONFIG.PREFIXES.USER}/stats`,
    ENABLE: `${API_CONFIG.PREFIXES.USER}/enable`,
    DISABLE: `${API_CONFIG.PREFIXES.USER}/disable`,
    RESET_PASSWORD: `${API_CONFIG.PREFIXES.USER}/reset-password`,
    ROLES: `${API_CONFIG.PREFIXES.USER}/roles`,
    ASSIGN_ROLES: `${API_CONFIG.PREFIXES.USER}/assign-roles`,
    REMOVE_ROLES: `${API_CONFIG.PREFIXES.USER}/remove-roles`,
  },
  
  // 当前用户相关
  ME: {
    GET: `${API_CONFIG.PREFIXES.ME}/get`,
    UPDATE: `${API_CONFIG.PREFIXES.ME}/update`,
    CHANGE_PASSWORD: `${API_CONFIG.PREFIXES.ME}/change-password`,
  },
  
  // 角色相关
  ROLE: {
    LIST: `${API_CONFIG.PREFIXES.ROLE}/list`,
    CREATE: `${API_CONFIG.PREFIXES.ROLE}/create`,
    GET: `${API_CONFIG.PREFIXES.ROLE}/get`,
    UPDATE: `${API_CONFIG.PREFIXES.ROLE}/update`,
    DELETE: `${API_CONFIG.PREFIXES.ROLE}/delete`,
    STATS: `${API_CONFIG.PREFIXES.ROLE}/stats`,
    PERMISSIONS: `${API_CONFIG.PREFIXES.ROLE}/permissions`,
    ASSIGN_PERMISSIONS: `${API_CONFIG.PREFIXES.ROLE}/assign-permissions`,
    REMOVE_PERMISSIONS: `${API_CONFIG.PREFIXES.ROLE}/remove-permissions`,
    USERS: `${API_CONFIG.PREFIXES.ROLE}/users`,
    ASSIGN: `${API_CONFIG.PREFIXES.ROLE}/assign`,
    REMOVE_USERS: `${API_CONFIG.PREFIXES.ROLE}/remove-users`,
  },
  
  // 权限相关
  PERMISSION: {
    LIST: `${API_CONFIG.PREFIXES.PERMISSION}/list`,
    CREATE: `${API_CONFIG.PREFIXES.PERMISSION}/create`,
    BATCH_CREATE: `${API_CONFIG.PREFIXES.PERMISSION}/batch-create`,
    GET: `${API_CONFIG.PREFIXES.PERMISSION}/get`,
    UPDATE: `${API_CONFIG.PREFIXES.PERMISSION}/update`,
    DELETE: `${API_CONFIG.PREFIXES.PERMISSION}/delete`,
    STATS: `${API_CONFIG.PREFIXES.PERMISSION}/stats`,
  },
  
  // 资源相关
  RESOURCE: {
    LIST: `${API_CONFIG.PREFIXES.RESOURCE}/list`,
    CREATE: `${API_CONFIG.PREFIXES.RESOURCE}/create`,
    GET: `${API_CONFIG.PREFIXES.RESOURCE}/get`,
    UPDATE: `${API_CONFIG.PREFIXES.RESOURCE}/update`,
    DELETE: `${API_CONFIG.PREFIXES.RESOURCE}/delete`,
    TREE: `${API_CONFIG.PREFIXES.RESOURCE}/tree`,
    PERMISSIONS: `${API_CONFIG.PREFIXES.RESOURCE}/permissions`,
    PERMISSIONS_CONFIGURE: `${API_CONFIG.PREFIXES.RESOURCE}/permissions/configure`,
    API_RESOURCES_AVAILABLE: `${API_CONFIG.PREFIXES.RESOURCE}/api-resources/available`,
    API_RESOURCES_ASSIGN: `${API_CONFIG.PREFIXES.RESOURCE}/api-resources/assign`,
    STATS: `${API_CONFIG.PREFIXES.RESOURCE}/stats`,
  },
  
  // 部门相关
  DEPARTMENT: {
    LIST: `${API_CONFIG.PREFIXES.DEPARTMENT}/list`,
    CREATE: `${API_CONFIG.PREFIXES.DEPARTMENT}/create`,
    GET: `${API_CONFIG.PREFIXES.DEPARTMENT}/get`,
    UPDATE: `${API_CONFIG.PREFIXES.DEPARTMENT}/update`,
    DELETE: `${API_CONFIG.PREFIXES.DEPARTMENT}/delete`,
    TREE: `${API_CONFIG.PREFIXES.DEPARTMENT}/tree`,
    STATS: `${API_CONFIG.PREFIXES.DEPARTMENT}/stats`,
    USERS: `${API_CONFIG.PREFIXES.DEPARTMENT}/users`,
    ASSIGN_USERS: `${API_CONFIG.PREFIXES.DEPARTMENT}/assign-users`,
    REMOVE_USERS: `${API_CONFIG.PREFIXES.DEPARTMENT}/remove-users`,
  },
  
  // 职位相关
  POSITION: {
    LIST: `${API_CONFIG.PREFIXES.POSITION}/list`,
    CREATE: `${API_CONFIG.PREFIXES.POSITION}/create`,
    GET: `${API_CONFIG.PREFIXES.POSITION}/get`,
    UPDATE: `${API_CONFIG.PREFIXES.POSITION}/update`,
    DELETE: `${API_CONFIG.PREFIXES.POSITION}/delete`,
    STATS: `${API_CONFIG.PREFIXES.POSITION}/stats`,
    ASSIGN: `${API_CONFIG.PREFIXES.POSITION}/assign`,
    BY_DEPARTMENT: `${API_CONFIG.PREFIXES.POSITION}/by-department`,
    USERS: `${API_CONFIG.PREFIXES.POSITION}/users`,
    REMOVE: `${API_CONFIG.PREFIXES.POSITION}/remove`,
  },

  // 租户相关
  TENANT: {
    LIST: `${API_CONFIG.PREFIXES.TENANT}/list`,
    CREATE: `${API_CONFIG.PREFIXES.TENANT}/create`,
    GET: `${API_CONFIG.PREFIXES.TENANT}/get`,
    UPDATE: `${API_CONFIG.PREFIXES.TENANT}/update`,
    DELETE: `${API_CONFIG.PREFIXES.TENANT}/delete`,
    UPDATE_STATUS: `${API_CONFIG.PREFIXES.TENANT}/update-status`,
    SEARCH: `${API_CONFIG.PREFIXES.TENANT}/search`,
  },
  
  // 验证码相关
  CAPTCHA: {
    GENERATE: `${API_CONFIG.PREFIXES.CAPTCHA}/generate`,
    VALIDATE: `${API_CONFIG.PREFIXES.CAPTCHA}/validate`,
  },
  
  // ID生成器相关
  IDGENERATOR: {
    // 序列管理
    SEQUENCES_LIST: `${API_CONFIG.PREFIXES.IDGENERATOR}/sequences/list`,
    SEQUENCES_DETAIL: `${API_CONFIG.PREFIXES.IDGENERATOR}/sequences/detail`,
    SEQUENCES_CREATE: `${API_CONFIG.PREFIXES.IDGENERATOR}/sequences/create`,
    SEQUENCES_UPDATE: `${API_CONFIG.PREFIXES.IDGENERATOR}/sequences/update`,
    SEQUENCES_DELETE: `${API_CONFIG.PREFIXES.IDGENERATOR}/sequences/delete`,
    SEQUENCES_PAUSE: `${API_CONFIG.PREFIXES.IDGENERATOR}/sequences/pause`,
    SEQUENCES_RESUME: `${API_CONFIG.PREFIXES.IDGENERATOR}/sequences/resume`,
    SEQUENCES_ALLOCATE: `${API_CONFIG.PREFIXES.IDGENERATOR}/sequences/allocate`,
    
    // ID生成
    GENERATE: `${API_CONFIG.PREFIXES.IDGENERATOR}/generate`,
    GENERATE_BATCH: `${API_CONFIG.PREFIXES.IDGENERATOR}/generate/batch`,
    
    // 业务类型管理
    BUSINESS_TYPES_LIST: `${API_CONFIG.PREFIXES.IDGENERATOR}/business-types/list`,
    BUSINESS_TYPES_APPLY: `${API_CONFIG.PREFIXES.IDGENERATOR}/business-types/apply`,
    BUSINESS_TYPES_APPLICATIONS: `${API_CONFIG.PREFIXES.IDGENERATOR}/business-types/applications`,
    BUSINESS_TYPES_APPROVE: `${API_CONFIG.PREFIXES.IDGENERATOR}/business-types/approve`,
    
    // 系统监控
    STATS: `${API_CONFIG.PREFIXES.IDGENERATOR}/stats`,
    METRICS: `${API_CONFIG.PREFIXES.IDGENERATOR}/metrics`,
    HEALTH: `${API_CONFIG.PREFIXES.IDGENERATOR}/health`,
    
    // 实用工具
    VALIDATE_BUSINESS_TYPE: `${API_CONFIG.PREFIXES.IDGENERATOR}/validate/business-type`,
    BATCH_PAUSE: `${API_CONFIG.PREFIXES.IDGENERATOR}/batch/pause`,
    BATCH_RESUME: `${API_CONFIG.PREFIXES.IDGENERATOR}/batch/resume`,
    
    // 系统配置
    CONFIG: `${API_CONFIG.PREFIXES.IDGENERATOR}/config`,
    CONFIG_UPDATE: `${API_CONFIG.PREFIXES.IDGENERATOR}/config/update`,
    LOGS: `${API_CONFIG.PREFIXES.IDGENERATOR}/logs`,
    PREVIEW_SEQUENCE: `${API_CONFIG.PREFIXES.IDGENERATOR}/preview/sequence`,
    EXPORT: `${API_CONFIG.PREFIXES.IDGENERATOR}/export`,
  },

  // 文件系统相关
  FILE_SYSTEM: {
    // 文件上传
    UPLOAD: `${API_CONFIG.PREFIXES.FILE_SYSTEM}/upload`,
    UPLOAD_TOKEN: `${API_CONFIG.PREFIXES.FILE_SYSTEM}/upload-token`,
    ACCESS_TOKEN: `${API_CONFIG.PREFIXES.FILE_SYSTEM}/access-token`,
    
    // 文件管理
    LIST: `${API_CONFIG.PREFIXES.FILE_SYSTEM}/records/list`,
    GET: `${API_CONFIG.PREFIXES.FILE_SYSTEM}/get`,
    DELETE: `${API_CONFIG.PREFIXES.FILE_SYSTEM}/delete`,
    MARK_PERMANENT: `${API_CONFIG.PREFIXES.FILE_SYSTEM}/mark-permanent`,
    
    // 场景配置管理
    CONFIG_CREATE: `${API_CONFIG.PREFIXES.FILE_SYSTEM}/config/create`,
    CONFIG_GET: `${API_CONFIG.PREFIXES.FILE_SYSTEM}/config/get`,
    CONFIG_LIST: `${API_CONFIG.PREFIXES.FILE_SYSTEM}/config/list`,
    CONFIG_UPDATE: `${API_CONFIG.PREFIXES.FILE_SYSTEM}/config/update`,
    CONFIG_DELETE: `${API_CONFIG.PREFIXES.FILE_SYSTEM}/config/delete`,
    
    // 文件访问
    ACCESS: `${API_CONFIG.PREFIXES.FILE_SYSTEM}/access`,
  },

  // 应用相关
  APPLICATION: {
    LIST: `${API_CONFIG.PREFIXES.APPLICATION}/list`,
    CREATE: `${API_CONFIG.PREFIXES.APPLICATION}/create`,
    GET: `${API_CONFIG.PREFIXES.APPLICATION}/get`,
    GET_BY_APP_ID: `${API_CONFIG.PREFIXES.APPLICATION}/get-by-app-id`,
    UPDATE: `${API_CONFIG.PREFIXES.APPLICATION}/update`,
    DELETE: `${API_CONFIG.PREFIXES.APPLICATION}/delete`,
    ENABLE: `${API_CONFIG.PREFIXES.APPLICATION}/enable`,
    DISABLE: `${API_CONFIG.PREFIXES.APPLICATION}/disable`,
    STATS: `${API_CONFIG.PREFIXES.APPLICATION}/stats`,
  },
  
  // 健康检查
  HEALTH: `${API_CONFIG.PREFIXES.HEALTH}`,
  
  // 公开接口
  PUBLIC: {
    RESET_PASSWORD: `${API_CONFIG.PREFIXES.PUBLIC}/reset-password`,
  },

  // 邮件服务相关
  EMAIL: {
    // 邮件账户管理
    ACCOUNTS_LIST: `${API_CONFIG.PREFIXES.EMAIL}/accounts/list`,
    ACCOUNTS_CREATE: `${API_CONFIG.PREFIXES.EMAIL}/accounts/create`,
    ACCOUNTS_UPDATE: `${API_CONFIG.PREFIXES.EMAIL}/accounts/update`,
    ACCOUNTS_DETAIL: `${API_CONFIG.PREFIXES.EMAIL}/accounts/detail`,
    ACCOUNTS_DELETE: `${API_CONFIG.PREFIXES.EMAIL}/accounts/delete`,
    ACCOUNTS_TEST: `${API_CONFIG.PREFIXES.EMAIL}/accounts/test`,
    ACCOUNTS_TYPES: `${API_CONFIG.PREFIXES.EMAIL}/accounts/types`,
    ACCOUNTS_TEST_STATUS: `${API_CONFIG.PREFIXES.EMAIL}/accounts/test-status`,
      // 邮件服务商（用于快捷填充）
      ACCOUNTS_PROVIDERS: `${API_CONFIG.PREFIXES.EMAIL}/accounts/providers`,
      ACCOUNTS_PROVIDER_DETAIL: `${API_CONFIG.PREFIXES.EMAIL}/accounts/provider/detail`,
    
    // 邮件模板管理 - 新重构接口
    TEMPLATES_SAVE: `${API_CONFIG.PREFIXES.EMAIL}/templates/save`,                    // 保存/更新基本信息
    TEMPLATES_CONTENT: `${API_CONFIG.PREFIXES.EMAIL}/templates/content`,              // 更新模板内容
    TEMPLATES_VARIABLES_UPDATE: `${API_CONFIG.PREFIXES.EMAIL}/templates/variables`,   // 更新模板变量
    
    // 邮件模板管理 - 保留接口
    TEMPLATES_LIST: `${API_CONFIG.PREFIXES.EMAIL}/templates/list`,
    TEMPLATES_GET: `${API_CONFIG.PREFIXES.EMAIL}/templates/get`,
    TEMPLATES_DELETE: `${API_CONFIG.PREFIXES.EMAIL}/templates/delete`,
    TEMPLATES_VARIABLES_GET: `${API_CONFIG.PREFIXES.EMAIL}/templates/variables/get`,
    TEMPLATES_PREVIEW: `${API_CONFIG.PREFIXES.EMAIL}/templates/preview`,
    TEMPLATES_CLONE: `${API_CONFIG.PREFIXES.EMAIL}/templates/clone`,
    
    // 邮件模板管理 - 保留接口
    TEMPLATES_SELECT: `${API_CONFIG.PREFIXES.EMAIL}/templates/select`,
  },

  // 验证服务相关
  VERIFICATION: {
    // 配置管理
    CONFIGS_LIST: `${API_CONFIG.PREFIXES.VERIFICATION}/configs`,
    CONFIGS_CREATE: `${API_CONFIG.PREFIXES.VERIFICATION}/configs/create`,
    CONFIGS_UPDATE: `${API_CONFIG.PREFIXES.VERIFICATION}/configs/update`,
    CONFIGS_DELETE: `${API_CONFIG.PREFIXES.VERIFICATION}/configs/delete`,
    CONFIGS_DETAIL: `${API_CONFIG.PREFIXES.VERIFICATION}/configs/detail`,
    CONFIGS_COPY: `${API_CONFIG.PREFIXES.VERIFICATION}/configs/copy`,
    CONFIGS_EFFECTIVE: `${API_CONFIG.PREFIXES.VERIFICATION}/configs/effective`,
    
    // 验证操作
    SEND: `${API_CONFIG.PREFIXES.VERIFICATION}/send`,
    VERIFY: `${API_CONFIG.PREFIXES.VERIFICATION}/verify`,
    RESEND: `${API_CONFIG.PREFIXES.VERIFICATION}/resend`,
    STATUS: `${API_CONFIG.PREFIXES.VERIFICATION}/status`,
    STATISTICS: `${API_CONFIG.PREFIXES.VERIFICATION}/statistics`,
  },
} as const;

// 环境配置
export const ENV_CONFIG = {
  // 开发环境
  DEVELOPMENT: {
    BASE_URL: '',
    API_PREFIX: '/api/user',
  },
  
  // 生产环境
  PRODUCTION: {
    BASE_URL: process.env.REACT_APP_API_BASE_URL || 'https://api.example.com',
    API_PREFIX: '/api/user',
  },
  
  // 测试环境
  TEST: {
    BASE_URL: 'http://test-api.example.com',
    API_PREFIX: '/api/user',
  },
} as const;

// 获取当前环境配置
export const getCurrentEnvConfig = () => {
  const env = process.env.NODE_ENV || 'development';
  switch (env) {
    case 'production':
      return ENV_CONFIG.PRODUCTION;
    case 'test':
      return ENV_CONFIG.TEST;
    default:
      return ENV_CONFIG.DEVELOPMENT;
  }
};

// 构建完整API URL
export const buildApiUrl = (endpoint: string): string => {
  // 在开发环境下，直接返回endpoint，让setupProxy.js处理代理
  if (process.env.NODE_ENV === 'development') {
    return endpoint;
  }
  
  // 生产环境下构建完整URL
  const envConfig = getCurrentEnvConfig();
  return `${envConfig.BASE_URL}${endpoint}`;
};

// 导出类型
export type ApiEndpoint = typeof API_ENDPOINTS;
export type ApiConfig = typeof API_CONFIG;

// ==================== TOKEN管理 ====================

// 获取访问令牌（从 localStorage 获取，确保跨窗口共享）
export const getAccessToken = (): string | null => {
  try {
    // 直接从 localStorage 获取访问令牌
    const token = localStorage.getItem('access_token');
    const expiryTime = localStorage.getItem('token_expiry');
    
    if (!token || !expiryTime) {
      return null;
    }

    // 检查是否过期
    if (Date.now() > parseInt(expiryTime)) {
      clearAuth();
      return null;
    }

    return token;
  } catch (error) {
    console.error('Failed to get access token:', error);
    return null;
  }
};

// 清除认证信息
export const clearAuth = (): void => {
  try {
    // 清除 localStorage 中的认证信息
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user_info');
    localStorage.removeItem('token_expiry');
    localStorage.removeItem('tenant_id');
  } catch (error) {
    console.error('Failed to clear auth:', error);
  }
};

// 获取全局未授权处理器（延迟导入避免循环依赖）
let globalUnauthorizedHandler: (() => void) | null = null;

export const setGlobalUnauthorizedHandler = (handler: () => void) => {
  globalUnauthorizedHandler = handler;
};

export const getGlobalUnauthorizedHandler = () => {
  return globalUnauthorizedHandler;
};

// ==================== HTTP客户端配置 ====================

/**
 * 获取baseURL配置
 * 开发环境：使用空字符串，通过setupProxy.js代理
 * 生产环境：使用环境变量或默认URL
 */
const getBaseURL = (): string => {
    const envConfig = getCurrentEnvConfig();
    
    // 开发环境下强制使用空字符串，确保走setupProxy.js代理
    if (process.env.NODE_ENV === 'development') {
        return ''; // 开发环境必须为空，所有请求都通过setupProxy.js代理转发
    }
    
    // 生产环境使用配置的URL
    return envConfig.BASE_URL || process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080';
};

/**
 * 请求配置
 */
const config: AxiosRequestConfig = {
    baseURL: getBaseURL(),
    timeout: API_CONFIG.TIMEOUT,
    headers: {
        ...API_CONFIG.HEADERS,
    },
};

/**
 * 创建axios实例
 */
const instance: AxiosInstance = axios.create(config);

/**
 * 请求拦截器
 */
instance.interceptors.request.use(
    (config) => {
        // 添加认证token
        const token = getAccessToken();
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        // 添加应用ID - 全局设置
        config.headers['X-App-Id'] = 'k3J8v1Q9s2L5n8W0p4X7aA';
        
        // 添加请求ID
        config.headers['X-Request-ID'] = generateRequestId();

        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

/**
 * 响应拦截器
 */
instance.interceptors.response.use(
    (response: AxiosResponse) => {
        // 直接返回响应数据，保持原有的响应结构
        return response;
    },
    (error) => {
        // 处理401未授权错误和403权限不足错误
        if (error.response?.status === HTTP_STATUS.UNAUTHORIZED || error.response?.status === HTTP_STATUS.FORBIDDEN) {
            const unauthorizedHandler = getGlobalUnauthorizedHandler();
            if (unauthorizedHandler) {
                // 使用React Router进行导航，保持路由状态
                unauthorizedHandler();
            } else {
                // 如果没有设置处理器，使用备用方案
                clearAuth();
                window.location.href = '/login';
            }
            return Promise.reject(error);
        }

        // 处理其他网络错误，但跳过404错误（由具体服务处理）
        if (error.response?.status !== 404) {
          const friendly = buildNetworkErrorMessage(error);
          try {
            (error as any).userMessage = friendly;
            if (typeof error.message === 'string') {
              error.message = friendly;
            }
          } catch (_) {
            // ignore
          }
        }
        return Promise.reject(error);
    }
);

/**
 * 根据错误构建用户友好的消息（不直接提示，交由调用方统一展示）
 */
const buildNetworkErrorMessage = (error: any): string => {
    if (error?.code === 'ECONNABORTED' || (typeof error?.message === 'string' && error.message.includes('timeout'))) {
        return '请求超时，请稍后重试';
    }
    if (error?.response) {
        const { status } = error.response;
        switch (status) {
            case 401:
                return '登录已过期，请重新登录';
            case 403:
                return '没有权限访问';
            case 404:
                return '请求的资源不存在';
            case 504:
                return '服务当前不可用，请稍后重试';
            case 500:
                return '服务器内部错误';
            default:
                return '网络错误，请稍后重试';
        }
    }
    return '网络连接失败，请检查网络设置';
};

/**
 * 生成请求ID
 */
const generateRequestId = (): string => {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// ==================== 导出BASIC HTTP方法 ====================

/**
 * 基础请求方法封装（兼容原有request对象）
 */
export const request = {
    get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
        return instance.get(url, config);
    },

    post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
        return instance.post(url, data, config);
    },

    put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
        return instance.put(url, data, config);
    },

    delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
        return instance.delete(url, config);
    },

    patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
        return instance.patch(url, data, config);
    },
};

// ==================== API服务方法 ====================

// 已在文件顶部导入，无需重复导入

/**
 * 类型安全的 API 服务方法，自动解包 data 字段，返回业务数据，遇到 code!=0 自动抛出错误
 */
export const apiService = {
  get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.get<ApiResponse<T>>(url, config).then(res => res.data);
  },
  post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.post<ApiResponse<T>>(url, data, config).then(res => res.data);
  },
  put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.put<ApiResponse<T>>(url, data, config).then(res => res.data);
  },
  delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return instance.delete<ApiResponse<T>>(url, config).then(res => res.data);
  },
  /**
   * 通用 request 方法，支持 method 选择
   */
  async request<T = any>(endpoint: string, method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'POST', data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const url = buildApiUrl(endpoint);
    let res: AxiosResponse<ApiResponse<T>>;
    switch (method) {
      case 'GET':
        res = await instance.get<ApiResponse<T>>(url, config);
        break;
      case 'POST':
        res = await instance.post<ApiResponse<T>>(url, data, config);
        break;
      case 'PUT':
        res = await instance.put<ApiResponse<T>>(url, data, config);
        break;
      case 'DELETE':
        res = await instance.delete<ApiResponse<T>>(url, config);
        break;
      default:
        res = await instance.post<ApiResponse<T>>(url, data, config);
    }
    return res.data;
  },
};

// ==================== 导出 ====================

// 导出axios实例（用于特殊情况）
export { instance as api };

// 导出类型
export type { AxiosInstance, AxiosRequestConfig, AxiosResponse };

// 默认导出
export default request;
